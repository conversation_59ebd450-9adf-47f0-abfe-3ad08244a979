我现在要开发一个项目流程管理系统，这里的“项目”你可以理解为销售成功签合同的订单，它的核心功能是管理和跟踪项目从创建到结束的全流程。这个流程中间有一系列操作，我们有4条产品线，分别是A,B,C,D，每条产品线对应的项目流程还不一样，
项目由销售发起并创建(主节点1)，
然后根据产品线自动分配对应的唯一主要执行部门(简称主部门)，其中D产品线的主部门要手动选择，因为它有两个主部门，确定好主部门后，节点流转到主部门唯一负责人，操作内容：选择主部门执行人员(主节点2)，
然后节点流转到主部门执行人员，操作内容：上传方案或资料(主节点3)，
然后节点流转到主部门唯一负责人，操作内容：审批由主部门执行人员提交过来的方案或资料(主节点4)，审核驳回，自动退回到主节点3，
审核通过，节点流转到销售，操作内容：更新产品信息(主节点5)，
这个节点非常关键，它有一个分支流程，销售在这一步修改项目签约状态，当为未中标，节点流转到对应的商务负责人，操作内容：审核项目未中标(主节点6)，项目直接结束。

以下是产品线A的中标后流程走向
如果修改项目签约状态为中标，节点流转到主部门负责人，操作内容：1.选择协同部门，2.确认合作内容与执行一致(主节点6)，协同部门可以选择多个，且不确定会选几个协同部门。选择协同部门后，每个协同部门会有自己的执行流程，协同部门的操作流程如下：
首先协同节点流转到协同部门负责人，操作内容：选择项目执行人员，更新数据内容(协同节点1)，
然后协同节点流转到协同部门项目执行人员，操作内容：选择数据分析师，更新数据内容(协同节点2)，
然后协同节点流转到协同部门数据分析师，操作内容：上传原始数据(协同节点3)，
然后协同节点流转到协同部门项目执行人员并抄送协同部门负责人，操作内容：审核原始数据(协同节点4)，审核驳回，退回到协同节点3，
审核通过，协同部门流程暂时结束，节点又流转到最初选定的主部门执行人员，操作内容：1.确认接受数据or驳回，2.支持追溯驳回(把已确认的驳回)，3.上传ppt(主节点7)，审核驳回，节点退回到协同节点3，审核通过，协同流程彻底结束。
然后节点流转到主部门负责人，操作内容：1.审批ppt，2.跟进客户or交付(主节点8)，审核驳回，退回到主节点7，
审核通过，项目结束，系统自动发送邮件给客户。

以下是产品线B的中标后流程走向
如果修改项目签约状态为中标，节点流转到主部门负责人，操作内容：选择主部门执行人员(主节点6)，
然后节点流转到主部门执行人员，操作内容：1.一键完成or上传文件，2.交付(主节点7)，项目结束，系统自动发送邮件给客户。

以下是产品线C的中标后流程走向
如果修改项目签约状态为中标，节点流转到主部门负责人，操作内容：1.选择协同部门，2.确认合作内容与执行一致(主节点6)，协同部门可以选择多个，且不确定会选几个协同部门。选择协同部门后，每个协同部门会有自己的执行流程，协同部门的操作流程如下：
首先协同节点流转到协同部门负责人，操作内容：选择项目执行人员(协同节点1)，
然后协同节点流转到协同部门执行人员，操作内容：上传原始数据(协同节点2)，
协同部门流程暂时结束，节点又流转到最初选定的主部门执行人员，操作内容：1.确认接受数据or驳回，2.支持追溯驳回(把已确认的驳回)，3.上传ppt(主节点7)，审核驳回，节点退回到协同节点2，审核通过，协同流程彻底结束。
然后节点流转到主部门负责人，操作内容：1.审批ppt，2.交付(主节点8)，审核驳回，退回到主节点7，
审核通过，项目结束，系统自动发送邮件给客户。


以下是产品线D的中标后流程走向
如果修改项目签约状态为中标，节点流转到主部门负责人，操作内容：1.选择协同部门，2.确认合作内容与执行一致(主节点6)，协同部门可以选择多个，且不确定会选几个协同部门。选择协同部门后，每个协同部门会有自己的执行流程，协同部门的操作流程如下：
首先协同节点流转到协同部门负责人，操作内容：选择项目执行人员(协同节点1)，
然后协同节点流转到协同部门项目执行人员，操作内容：上传原始数据(协同节点2)，
然后协同节点流转到协同部门负责人，操作内容：审核原始数据(协同节点3)，审核驳回，退回到协同节点2，
审核通过，协同部门流程暂时结束，节点又流转到最初选定的主部门执行人员，操作内容：1.数据质检，上传ppt(主节点7)，
然后节点流转到主部门负责人，操作内容：1.审批ppt(可跳过审批)，2.跟进客户or交付(主节点8)，审核驳回，退回到主节点7，
审核通过，项目结束，系统自动发送邮件给客户。


所有的驳回操作，重置目标节点状态，以及目标节点以后所有节点状态。

注意：
这里除了产品线B明确不需要协同部门，其余产品线均由(中标后的主节点6)的主部门负责人在后台选择是否需要协同部门，但不一定选，选了就走协同流程，不选就继续走主流程。
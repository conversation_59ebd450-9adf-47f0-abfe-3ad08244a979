<?php

// 简单的API测试脚本
require_once 'vendor/autoload.php';

// 设置环境
$_ENV['CI_ENVIRONMENT'] = 'development';

// 初始化CodeIgniter
$app = \Config\Services::codeigniter();
$app->initialize();

// 创建控制器实例
$controller = new \App\Controllers\TemplateController();

try {
    // 调用getNodeTemplates方法
    $response = $controller->getNodeTemplates();
    
    // 获取响应体
    $body = $response->getBody();
    
    echo "API Response:\n";
    echo $body . "\n";
    
    // 解析JSON
    $data = json_decode($body, true);
    
    if ($data) {
        echo "\nParsed Data:\n";
        echo "Status: " . ($data['status'] ?? 'unknown') . "\n";
        echo "Message: " . ($data['message'] ?? 'unknown') . "\n";
        echo "Data count: " . (is_array($data['data']) ? count($data['data']) : 'not array') . "\n";
        
        if (is_array($data['data']) && count($data['data']) > 0) {
            echo "\nFirst item:\n";
            print_r($data['data'][0]);
        }
    } else {
        echo "Failed to parse JSON response\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
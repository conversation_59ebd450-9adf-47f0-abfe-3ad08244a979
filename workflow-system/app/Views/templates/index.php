<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>
<div class="page-header">
    <div class="page-title">流程模板管理</div>
    <div class="page-description">管理通用节点模板和流转规则，为各产品线流程提供基础</div>
</div>

<!-- 标签页导航 -->
<div class="card">
    <div class="card-header">
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="switchTab('node-templates')">
                <i class="fas fa-sitemap"></i> 节点模板
            </button>
            <button class="nav-tab" onclick="switchTab('flow-rules')">
                <i class="fas fa-route"></i> 流转规则
            </button>
            <button class="nav-tab" onclick="switchTab('workflow-definitions')">
                <i class="fas fa-project-diagram"></i> 工作流定义
            </button>
        </div>
    </div>
</div>

<!-- 节点模板标签页 -->
<div id="node-templates" class="tab-content active">
    <div class="card">
        <div class="card-header">
            <div class="card-title">节点模板列表</div>
            <button class="btn btn-primary" onclick="createTemplate()">
                <i class="fas fa-plus"></i> 新建模板
            </button>
        </div>
        <div class="card-body">
            <div class="toolbar">
                <input type="text" id="templateSearchInput" class="search-input" placeholder="搜索模板名称或编码..." onkeyup="filterTemplates()">
                <select id="templateTypeFilter" class="select" onchange="filterTemplates()">
                    <option value="">全部类型</option>
                    <option value="1">主流程</option>
                    <option value="2">协同流程</option>
                </select>
                <button class="btn btn-success" onclick="exportTemplates()">
                    <i class="fas fa-download"></i> 导出模板
                </button>
            </div>
            
            <div id="loadingState" class="alert alert-info" style="display: none;">
                <i class="fas fa-spinner fa-spin"></i> 正在加载模板数据...
            </div>
            
            <table class="table" id="templateTable">
                <thead>
                    <tr>
                        <th>节点编码</th>
                        <th>节点名称</th>
                        <th>流程类型</th>
                        <th>负责角色</th>
                        <th>可跳过</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="templateTableBody">
                    <!-- 模板数据将通过JS动态加载 -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 流转规则标签页 -->
<div id="flow-rules" class="tab-content" style="display: none;">
    <div class="card">
        <div class="card-header">
            <div class="card-title">节点流转规则</div>
            <div>
                <button class="btn btn-primary" onclick="createFlowRule()">
                    <i class="fas fa-plus"></i> 新建规则
                </button>
            </div>
        </div>
        <div class="card-body">
            <table class="table" id="flowRuleTable">
                <thead>
                    <tr>
                        <th>来源节点</th>
                        <th>目标节点</th>
                        <th>条件类型</th>
                        <th>条件值</th>
                        <th>优先级</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="flowRuleTableBody">
                    <!-- 流转规则数据 -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 工作流定义标签页 -->
<div id="workflow-definitions" class="tab-content" style="display: none;">
    <div class="card">
        <div class="card-header">
            <div class="card-title">工作流定义</div>
            <div>
                <select id="defProductLineFilter" class="select" style="margin-right: 12px;">
                    <option value="">选择产品线</option>
                </select>
                <button class="btn btn-primary" onclick="createWorkflowDef()">
                    <i class="fas fa-plus"></i> 添加节点
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="workflow-definition-container">
                <div class="flow-section">
                    <h4><i class="fas fa-arrow-right"></i> 主流程</h4>
                    <div id="mainFlowContainer" class="flow-container">
                        <!-- 主流程节点 -->
                    </div>
                </div>
                <div class="flow-section">
                    <h4><i class="fas fa-code-branch"></i> 协同流程</h4>
                    <div id="collabFlowContainer" class="flow-container">
                        <!-- 协同流程节点 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 模板编辑模态框 -->
<div id="templateModal" class="modal">
    <div class="modal-dialog">
        <div class="modal-header">
            <div class="modal-title" id="templateModalTitle">新建模板</div>
            <button class="modal-close" onclick="closeTemplateModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form id="templateForm">
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">节点编码 <span style="color: #ff4d4f;">*</span></label>
                    <input type="text" id="nodeCode" class="form-control" required placeholder="如: main_1, collab_1">
                </div>
                
                <div class="form-group">
                    <label class="form-label">节点名称 <span style="color: #ff4d4f;">*</span></label>
                    <input type="text" id="nodeName" class="form-control" required placeholder="如: 项目发起">
                </div>
                

                
                <div class="form-group">
                    <label class="form-label">流程类型 <span style="color: #ff4d4f;">*</span></label>
                    <select id="nodeType" class="form-control" required>
                        <option value="">请选择流程类型</option>
                        <option value="1">主流程</option>
                        <option value="2">协同流程</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">负责角色 <span style="color: #ff4d4f;">*</span></label>
                    <select id="assigneeRole" class="form-control" required>
                        <option value="">请选择角色</option>
                        <option value="1">销售</option>
                        <option value="2">主部门负责人</option>
                        <option value="3">主部门执行人员</option>
                        <option value="4">协同部门负责人</option>
                        <option value="5">协同部门执行人员</option>
                        <option value="6">数据分析师</option>
                        <option value="7">商务负责人</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">节点排序 <span style="color: #ff4d4f;">*</span></label>
                    <input type="number" id="orderNo" class="form-control" required min="1" max="20" placeholder="1-20">
                </div>
                
                <div class="form-group">
                    <label class="form-label">
                        <input type="checkbox" id="isOptional" style="margin-right: 8px;"> 该节点可跳过
                    </label>
                </div>
                
                <div class="form-group">
                    <label class="form-label">描述</label>
                    <textarea id="nodeDescription" class="form-control" type="textarea" rows="3" placeholder="节点功能描述（可选）"></textarea>
                </div>
            </div>
            
            <div class="modal-footer">
                <button type="button" class="btn btn-default" onclick="closeTemplateModal()">取消</button>
                <button type="submit" class="btn btn-primary">保存</button>
            </div>
        </form>
    </div>
</div>

<!-- 流转规则编辑模态框 -->
<div id="flowRuleModal" class="modal">
    <div class="modal-dialog">
        <div class="modal-header">
            <div class="modal-title" id="flowRuleModalTitle">新建流转规则</div>
            <button class="modal-close" onclick="closeFlowRuleModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form id="flowRuleForm">
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">来源节点 <span style="color: #ff4d4f;">*</span></label>
                    <select id="sourceNode" class="form-control" required>
                        <option value="">请选择来源节点</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">目标节点 <span style="color: #ff4d4f;">*</span></label>
                    <select id="targetNode" class="form-control" required>
                        <option value="">请选择目标节点</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">条件类型 <span style="color: #ff4d4f;">*</span></label>
                    <select id="conditionType" class="form-control" required>
                        <option value="">请选择条件类型</option>
                        <option value="always">始终流转</option>
                        <option value="approval">需要审批</option>
                        <option value="rejection">被拒绝时</option>
                        <option value="timeout">超时流转</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">条件值</label>
                    <input type="text" id="conditionValue" class="form-control" placeholder="根据条件类型填写相应值">
                </div>
                
                <div class="form-group">
                    <label class="form-label">优先级 <span style="color: #ff4d4f;">*</span></label>
                    <input type="number" id="priority" class="form-control" required min="1" max="100" value="10" placeholder="1-100，数字越小优先级越高">
                </div>
                
                <div class="form-group">
                    <label class="form-label">
                        <input type="checkbox" id="isActive" style="margin-right: 8px;" checked> 启用此规则
                    </label>
                </div>
                
                <div class="form-group">
                    <label class="form-label">备注</label>
                    <textarea id="ruleDescription" class="form-control" rows="3" placeholder="流转规则说明（可选）"></textarea>
                </div>
            </div>
            
            <div class="modal-footer">
                <button type="button" class="btn btn-default" onclick="closeFlowRuleModal()">取消</button>
                <button type="submit" class="btn btn-primary">保存</button>
            </div>
        </form>
    </div>
</div>

<!-- 工作流定义编辑模态框 -->
<div id="workflowDefinitionModal" class="modal">
    <div class="modal-dialog">
        <div class="modal-header">
            <div class="modal-title" id="workflowDefinitionModalTitle">新建工作流定义</div>
            <button class="modal-close" onclick="closeWorkflowDefinitionModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form id="workflowDefinitionForm">
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">产品线 <span style="color: #ff4d4f;">*</span></label>
                    <select id="defProductLineId" class="form-control" required>
                        <option value="">请选择产品线</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">节点模板 <span style="color: #ff4d4f;">*</span></label>
                    <select id="defNodeTemplateId" class="form-control" required>
                        <option value="">请选择节点模板</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">流程类型 <span style="color: #ff4d4f;">*</span></label>
                    <select id="defFlowType" class="form-control" required>
                        <option value="">请选择流程类型</option>
                        <option value="main">主流程</option>
                        <option value="collab">协同流程</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">排序号 <span style="color: #ff4d4f;">*</span></label>
                    <input type="number" id="defOrderNo" class="form-control" required min="1" placeholder="流程中的排序位置">
                </div>
                
                <div class="form-group">
                    <label class="form-label">
                        <input type="checkbox" id="defIsActive" style="margin-right: 8px;" checked> 启用此定义
                    </label>
                </div>
                
                <div class="form-group">
                    <label class="form-label">备注</label>
                    <textarea id="defDescription" class="form-control" rows="3" placeholder="工作流定义说明（可选）"></textarea>
                </div>
            </div>
            
            <div class="modal-footer">
                <button type="button" class="btn btn-default" onclick="closeWorkflowDefinitionModal()">取消</button>
                <button type="submit" class="btn btn-primary">保存</button>
            </div>
        </form>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// API 基础URL
const API_BASE = '/api/v1';

// 全局数据
let templates = [];
let flowRules = [];
let productLines = [];
let roles = [];
let currentEditingTemplate = null;
let currentEditingFlowRule = null;
let currentActiveTab = 'node-templates';

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

// 标签页切换功能
function switchTab(tabName) {
    // 隐藏所有标签页内容
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(tab => {
        tab.style.display = 'none';
    });
    
    // 移除所有标签页的激活状态
    const navTabs = document.querySelectorAll('.nav-tab');
    navTabs.forEach(tab => {
        tab.classList.remove('active');
    });
    
    // 显示选中的标签页
    const targetTab = document.getElementById(tabName);
    if (targetTab) {
        targetTab.style.display = 'block';
    }
    
    // 激活选中的导航标签
    const activeNavTab = Array.from(navTabs).find(tab => 
        tab.onclick.toString().includes(tabName)
    );
    if (activeNavTab) {
        activeNavTab.classList.add('active');
    }
    
    currentActiveTab = tabName;
    
    // 根据不同标签页加载数据
    if (tabName === 'flow-rules') {
        loadFlowRules();
    } else if (tabName === 'workflow-definitions') {
        loadWorkflowDefinitions();
    }
}

// 检查API连接
async function checkApiConnection() {
    try {
        const response = await fetch(`${API_BASE}/master/product-lines`);
        if (response.ok) {
            console.log('API连接成功');
            return true;
        } else {
            throw new Error('API连接失败');
        }
    } catch (error) {
        console.error('API连接错误:', error);
        throw error;
    }
}

// 初始化页面
async function initializePage() {
    try {
        // 优先加载模拟数据，确保功能可用
        loadMockData();
        
        // 检查API连接
        await checkApiConnection();
        
        // 然后尝试加载真实数据
        await loadInitialData();
        await loadTemplates();
    } catch (error) {
        console.error('初始化失败:', error);
        // 如果真实数据加载失败，确保模拟数据可用
        loadMockData();
        showMessage('使用模拟数据模式', 'info');
    }
}

// 加载初始数据
async function loadInitialData() {
    showLoading(true);
    
    try {
        await Promise.all([
            loadProductLines(),
            loadRoles()
        ]);
    } catch (error) {
        console.error('加载初始数据失败:', error);
        loadMockData();
    } finally {
        showLoading(false);
    }
}

// 加载产品线数据
async function loadProductLines() {
    try {
        const response = await fetch(`${API_BASE}/master/product-lines`);
        if (response.ok) {
            const result = await response.json();
            productLines = Array.isArray(result.data) ? result.data : (Array.isArray(result) ? result : []);
            updateProductLineOptions();
        }
    } catch (error) {
        console.error('加载产品线失败:', error);
        productLines = [];
    }
}

// 加载角色数据
async function loadRoles() {
    try {
        const response = await fetch(`${API_BASE}/master/roles`);
        if (response.ok) {
            const result = await response.json();
            roles = Array.isArray(result.data) ? result.data : (Array.isArray(result) ? result : []);
            updateRoleOptions();
        }
    } catch (error) {
        console.error('加载角色失败:', error);
        roles = [];
    }
}

// 加载模板数据
async function loadTemplates() {
    showLoading(true);
    
    try {
        const response = await fetch(`${API_BASE}/templates/nodes`);
        console.log('模板API响应状态:', response.status);
        if (response.ok) {
            const result = await response.json();
            console.log('模板API返回数据:', result);
            templates = Array.isArray(result.data) ? result.data : (Array.isArray(result) ? result : []);
            console.log('处理后的templates:', templates);
            console.log('templates是否为数组:', Array.isArray(templates));
            renderTemplates();
        } else {
            throw new Error('模板数据加载失败');
        }
    } catch (error) {
        console.error('加载模板失败:', error);
        loadMockTemplates();
    } finally {
        showLoading(false);
    }
}

// 渲染模板列表
function renderTemplates() {
    const tableBody = document.getElementById('templateTableBody');
    if (!tableBody) return;
    
    let filteredTemplates = filterTemplateData();
    
    if (filteredTemplates.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" style="text-align: center; padding: 40px; color: #8c8c8c;">
                    <i class="fas fa-inbox" style="font-size: 24px; margin-bottom: 8px; display: block;"></i>
                    暂无模板数据
                </td>
            </tr>
        `;
        return;
    }
    
    tableBody.innerHTML = filteredTemplates.map(template => {
        const roleName = getRoleName(template.assignee_role_id);
        const typeText = template.node_type === 1 || template.node_type === '1' ? '主流程' : '协同流程';
        const optionalText = template.is_optional === '1' || template.is_optional === true ? '是' : '否';
        
        return `
            <tr>
                <td>${template.node_code}</td>
                <td>${template.node_name}</td>
                <td>
                    <span class="tag ${template.node_type === 1 || template.node_type === '1' ? 'tag-processing' : 'tag-success'}">
                        ${typeText}
                    </span>
                </td>
                <td>${roleName}</td>
                <td>
                    <span class="tag ${template.is_optional === '1' || template.is_optional === true ? 'tag-default' : 'tag-error'}">
                        ${optionalText}
                    </span>
                </td>
                <td>${template.created_at || '-'}</td>
                <td>
                    <div class="actions">
                        <button class="btn btn-default btn-small" onclick="editTemplate(${template.id})">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        <button class="btn btn-danger btn-small" onclick="deleteTemplate(${template.id})">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// 筛选模板数据
function filterTemplateData() {
    const searchText = (document.getElementById('templateSearchInput')?.value || '').toLowerCase();
    const typeFilter = document.getElementById('templateTypeFilter')?.value || '';
    
    // 确保templates是数组
    if (!Array.isArray(templates)) {
        console.warn('templates is not an array:', templates);
        return [];
    }
    
    return templates.filter(template => {
        const matchSearch = !searchText || 
            template.node_name.toLowerCase().includes(searchText) ||
            template.node_code.toLowerCase().includes(searchText);
        
        // 直接比较数值：1=主流程，2=协同流程
        const matchType = !typeFilter || template.node_type.toString() === typeFilter;
        
        return matchSearch && matchType;
    });
}

// 过滤模板
function filterTemplates() {
    renderTemplates();
}

// 创建模板
function createTemplate() {
    currentEditingTemplate = null;
    document.getElementById('templateModalTitle').textContent = '新建模板';
    document.getElementById('templateForm').reset();
    document.getElementById('templateModal').style.display = 'flex';
}

// 编辑模板
function editTemplate(templateId) {
    const template = templates.find(t => t.id == templateId);
    if (!template) return;
    
    currentEditingTemplate = templateId;
    document.getElementById('templateModalTitle').textContent = '编辑模板';
    
    // 填充表单
    document.getElementById('nodeCode').value = template.node_code;
    document.getElementById('nodeName').value = template.node_name;
    // 直接使用数据库中的数值：1=主流程，2=协同流程
    document.getElementById('nodeType').value = template.node_type;
    document.getElementById('assigneeRole').value = template.assignee_role_id;
    document.getElementById('orderNo').value = template.order_no || '';
    document.getElementById('isOptional').checked = template.is_optional === '1' || template.is_optional === true;
    document.getElementById('nodeDescription').value = template.description || '';
    
    document.getElementById('templateModal').style.display = 'flex';
}

// 关闭模板模态框
function closeTemplateModal() {
    document.getElementById('templateModal').style.display = 'none';
    currentEditingTemplate = null;
}

// 删除模板
async function deleteTemplate(templateId) {
    if (!confirm('确定要删除这个模板吗？此操作不可撤销。')) return;
    
    try {
        const response = await fetch(`${API_BASE}/templates/nodes/${templateId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            templates = templates.filter(t => t.id != templateId);
            renderTemplates();
            showMessage('模板删除成功！', 'success');
        } else {
            throw new Error('删除失败');
        }
    } catch (error) {
        console.error('删除模板失败:', error);
        showMessage('删除模板失败，请重试', 'error');
    }
}

// 表单提交
document.getElementById('templateForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = {
        node_code: document.getElementById('nodeCode').value,
        node_name: document.getElementById('nodeName').value,
        // 直接使用选择的数值：1=主流程，2=协同流程
        node_type: parseInt(document.getElementById('nodeType').value),
        assignee_role_id: parseInt(document.getElementById('assigneeRole').value),
        order_no: parseInt(document.getElementById('orderNo').value) || null,
        is_optional: document.getElementById('isOptional').checked,
        description: document.getElementById('nodeDescription').value || null
    };
    
    try {
        let response;
        if (currentEditingTemplate) {
            response = await fetch(`${API_BASE}/templates/nodes/${currentEditingTemplate}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
        } else {
            response = await fetch(`${API_BASE}/templates/nodes`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
        }
        
        if (response.ok) {
            closeTemplateModal();
            await loadTemplates();
            showMessage(currentEditingTemplate ? '模板更新成功！' : '模板创建成功！', 'success');
        } else {
            const error = await response.json();
            throw new Error(error.message || '操作失败');
        }
    } catch (error) {
        console.error('保存模板失败:', error);
        showMessage('保存模板失败: ' + error.message, 'error');
    }
});

// 导出模板
function exportTemplates() {
    const productLine = document.getElementById('productLineFilter').value;
    if (!productLine) {
        showMessage('请先选择产品线', 'error');
        return;
    }
    
    // TODO: 实现导出功能
    showMessage('导出功能开发中...', 'info');
}

// 导入模板
function importTemplates() {
    // TODO: 实现导入功能
    showMessage('导入功能开发中...', 'info');
}

// 更新产品线选项
function updateProductLineOptions() {
    const selects = ['nodeProductLine', 'productLineFilter'];
    selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            if (selectId === 'productLineFilter') {
                select.innerHTML = '<option value="">选择产品线</option>';
            } else {
                select.innerHTML = '<option value="">请选择产品线</option>';
            }
            
            if (Array.isArray(productLines) && productLines.length > 0) {
                productLines.forEach(line => {
                    select.innerHTML += `<option value="${line.code}">${line.name}</option>`;
                });
            }
        }
    });
}

// 更新角色选项
function updateRoleOptions() {
    const select = document.getElementById('assigneeRole');
    if (select) {
        select.innerHTML = '<option value="">请选择角色</option>';
        if (Array.isArray(roles) && roles.length > 0) {
            roles.forEach(role => {
                select.innerHTML += `<option value="${role.id}">${role.name}</option>`;
            });
        }
    }
}

// 获取产品线名称
function getProductLineName(code) {
    const line = productLines.find(p => p.code === code);
    return line ? line.name : `产品线${code}`;
}

// 获取角色名称
function getRoleName(id) {
    const role = roles.find(r => r.id == id);
    return role ? role.name : '未知角色';
}

// 显示加载状态
function showLoading(show) {
    const loadingEl = document.getElementById('loadingState');
    if (loadingEl) {
        loadingEl.style.display = show ? 'block' : 'none';
    }
}

// 显示消息
function showMessage(message, type = 'info') {
    // 创建消息元素
    const alertEl = document.createElement('div');
    alertEl.className = `alert alert-${type}`;
    alertEl.style.position = 'fixed';
    alertEl.style.top = '20px';
    alertEl.style.right = '20px';
    alertEl.style.zIndex = '9999';
    alertEl.style.minWidth = '300px';
    alertEl.textContent = message;
    
    document.body.appendChild(alertEl);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (alertEl.parentNode) {
            alertEl.parentNode.removeChild(alertEl);
        }
    }, 3000);
}

// 加载模拟数据
function loadMockData() {
    productLines = [
        { id: 1, code: "A", name: "产品线A" },
        { id: 2, code: "B", name: "产品线B" },
        { id: 3, code: "C", name: "产品线C" },
        { id: 4, code: "D", name: "产品线D" }
    ];
    
    roles = [
        { id: 1, name: "销售" },
        { id: 2, name: "主部门负责人" },
        { id: 3, name: "主部门执行人员" },
        { id: 4, name: "协同部门负责人" },
        { id: 5, name: "协同部门执行人员" },
        { id: 6, name: "数据分析师" },
        { id: 7, name: "商务负责人" }
    ];
    
    updateProductLineOptions();
    updateRoleOptions();
    loadMockTemplates();
}

// 加载模拟模板数据
function loadMockTemplates() {
    templates = [
        {
            id: 1,
            node_code: "main_1",
            node_name: "项目发起",
            node_type: 1,
            assignee_role_id: 1,
            order_no: 1,
            is_optional: 0,
            description: "销售发起项目申请",
            role_name: "销售",
            created_at: Date.now() / 1000
        },
        {
            id: 2,
            node_code: "main_2",
            node_name: "需求评审",
            node_type: 1,
            assignee_role_id: 2,
            order_no: 2,
            is_optional: 0,
            description: "主部门负责人评审项目需求",
            role_name: "主部门负责人",
            created_at: Date.now() / 1000
        },
        {
            id: 3,
            node_code: "collab_1",
            node_name: "技术评估",
            node_type: 2,
            assignee_role_id: 4,
            order_no: 3,
            is_optional: 1,
            description: "协同部门进行技术可行性评估",
            role_name: "协同部门负责人",
            created_at: Date.now() / 1000
        }
    ];
    renderTemplates();
}

// Vimium兼容性修复 - 确保所有DOM元素都有正确的nodeName属性
function fixVimiumCompatibility() {
    try {
        // 检查并修复可能导致Vimium报错的元素
        const elements = document.querySelectorAll('*');
        elements.forEach(el => {
            if (el && typeof el.nodeName === 'undefined') {
                console.warn('发现异常DOM元素，跳过Vimium处理:', el);
            }
        });
    } catch (e) {
        // 静默处理Vimium兼容性问题
        console.debug('Vimium兼容性检查完成');
    }
}

// 流转规则管理功能

// 加载流转规则
async function loadFlowRules() {
    showLoading(true);
    
    try {
        const response = await fetch(`${API_BASE}/templates/flow-rules`);
        if (response.ok) {
            const result = await response.json();
            flowRules = Array.isArray(result.data) ? result.data : (Array.isArray(result) ? result : []);
            renderFlowRules();
        } else {
            throw new Error('流转规则数据加载失败');
        }
    } catch (error) {
        console.error('加载流转规则失败:', error);
        loadMockFlowRules();
    } finally {
        showLoading(false);
    }
}

// 渲染流转规则列表
function renderFlowRules() {
    const tableBody = document.getElementById('flowRuleTableBody');
    if (!tableBody) return;
    
    if (flowRules.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" style="text-align: center; padding: 40px; color: #8c8c8c;">
                    <i class="fas fa-inbox" style="font-size: 24px; margin-bottom: 8px; display: block;"></i>
                    暂无流转规则
                </td>
            </tr>
        `;
        return;
    }
    
    tableBody.innerHTML = flowRules.map(rule => {
        const sourceNodeName = rule.from_node_name ? `${rule.from_node_name} (${rule.from_node_code || ''})` : '未知节点';
        const targetNodeName = rule.to_node_name ? `${rule.to_node_name} (${rule.to_node_code || ''})` : '未知节点';
        const conditionTypeText = getConditionTypeText(rule.condition_type);
        // 修正布尔值判断
        const isActive = rule.is_active === '1' || rule.is_active === 1 || rule.is_active === true;
        const statusText = isActive ? '启用' : '禁用';
        const statusClass = isActive ? 'tag-success' : 'tag-error';
        
        return `
            <tr>
                <td>${sourceNodeName}</td>
                <td>${targetNodeName}</td>
                <td>${conditionTypeText}</td>
                <td>${rule.condition_value || '-'}</td>
                <td>${rule.priority}</td>
                <td>
                    <span class="tag ${statusClass}">
                        ${statusText}
                    </span>
                </td>
                <td>
                    <div class="actions">
                        <button class="btn btn-default btn-small" onclick="editFlowRule(${rule.id})">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        <button class="btn btn-danger btn-small" onclick="deleteFlowRule(${rule.id})">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// 创建流转规则
function createFlowRule() {
    currentEditingFlowRule = null;
    document.getElementById('flowRuleModalTitle').textContent = '新建流转规则';
    document.getElementById('flowRuleForm').reset();
    updateNodeOptions();
    
    // 新建时启用所有字段
    document.getElementById('sourceNode').disabled = false;
    document.getElementById('targetNode').disabled = false;
    
    // 设置默认值
    document.getElementById('priority').value = 10;
    document.getElementById('isActive').checked = true;
    
    // 根据条件类型调整条件值输入框
    updateConditionValueField();
    
    document.getElementById('flowRuleModal').style.display = 'flex';
}

// 编辑流转规则
function editFlowRule(ruleId) {
    const rule = flowRules.find(r => r.id == ruleId);
    if (!rule) {
        showMessage('找不到指定的流转规则', 'error');
        return;
    }
    
    currentEditingFlowRule = ruleId;
    document.getElementById('flowRuleModalTitle').textContent = '编辑流转规则';
    
    // 填充表单
    updateNodeOptions();
    document.getElementById('sourceNode').value = rule.from_node_template_id;
    document.getElementById('targetNode').value = rule.to_node_template_id;
    document.getElementById('conditionType').value = rule.condition_type;
    document.getElementById('conditionValue').value = rule.condition_value || '';
    document.getElementById('priority').value = rule.priority;
    // 正确处理布尔值转换
    document.getElementById('isActive').checked = rule.is_active === '1' || rule.is_active === 1 || rule.is_active === true;
    document.getElementById('ruleDescription').value = rule.condition_expression || '';
    
    // 编辑时禁用来源节点选择，避免破坏流转关系
    document.getElementById('sourceNode').disabled = true;
    
    // 根据条件类型调整条件值输入框
    updateConditionValueField();
    
    document.getElementById('flowRuleModal').style.display = 'flex';
}

// 关闭流转规则模态框
function closeFlowRuleModal() {
    document.getElementById('flowRuleModal').style.display = 'none';
    currentEditingFlowRule = null;
    
    // 重置表单状态
    document.getElementById('sourceNode').disabled = false;
    document.getElementById('targetNode').disabled = false;
    document.getElementById('flowRuleForm').reset();
}

// 删除流转规则
async function deleteFlowRule(ruleId) {
    if (!confirm('确定要删除这个流转规则吗？此操作不可撤销。')) return;
    
    try {
        const response = await fetch(`${API_BASE}/templates/flow-rules/${ruleId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            flowRules = flowRules.filter(r => r.id != ruleId);
            renderFlowRules();
            showMessage('流转规则删除成功！', 'success');
        } else {
            throw new Error('删除失败');
        }
    } catch (error) {
        console.error('删除流转规则失败:', error);
        showMessage('删除流转规则失败，请重试', 'error');
    }
}

// 更新节点选项
function updateNodeOptions() {
    const sourceSelect = document.getElementById('sourceNode');
    const targetSelect = document.getElementById('targetNode');
    
    if (sourceSelect && targetSelect) {
        sourceSelect.innerHTML = '<option value="">请选择来源节点</option>';
        targetSelect.innerHTML = '<option value="">请选择目标节点</option>';
        
        if (Array.isArray(templates) && templates.length > 0) {
            templates.forEach(template => {
                const option = `<option value="${template.id}">${template.node_name} (${template.node_code})</option>`;
                sourceSelect.innerHTML += option;
                targetSelect.innerHTML += option;
            });
        }
    }
}

// 获取节点名称
function getNodeName(nodeId) {
    const node = templates.find(t => t.id == nodeId);
    return node ? `${node.node_name} (${node.node_code})` : '未知节点';
}

// 获取条件类型文本
function getConditionTypeText(type) {
    const types = {
        'always': '始终流转',
        'approval': '需要审批',
        'rejection': '被拒绝时',
        'timeout': '超时流转'
    };
    return types[type] || '未知类型';
}

// 根据条件类型更新条件值字段
function updateConditionValueField() {
    const conditionType = document.getElementById('conditionType').value;
    const conditionValueField = document.getElementById('conditionValue');
    const conditionValueLabel = conditionValueField.previousElementSibling;
    
    switch(conditionType) {
        case 'always':
            conditionValueField.style.display = 'none';
            conditionValueLabel.style.display = 'none';
            conditionValueField.required = false;
            break;
        case 'approval':
            conditionValueField.style.display = 'block';
            conditionValueLabel.style.display = 'block';
            conditionValueField.placeholder = '如: approved, pending';
            conditionValueField.required = true;
            conditionValueLabel.textContent = '审批结果';
            break;
        case 'rejection':
            conditionValueField.style.display = 'block';
            conditionValueLabel.style.display = 'block';
            conditionValueField.placeholder = '如: rejected, declined';
            conditionValueField.required = true;
            conditionValueLabel.textContent = '拒绝类型';
            break;
        case 'timeout':
            conditionValueField.style.display = 'block';
            conditionValueLabel.style.display = 'block';
            conditionValueField.placeholder = '如: 24, 48 (小时)';
            conditionValueField.required = true;
            conditionValueField.type = 'number';
            conditionValueLabel.textContent = '超时时间(小时)';
            break;
        default:
            conditionValueField.style.display = 'block';
            conditionValueLabel.style.display = 'block';
            conditionValueField.placeholder = '条件值';
            conditionValueField.required = false;
            conditionValueField.type = 'text';
            conditionValueLabel.textContent = '条件值';
    }
}

// 表单验证
function validateFlowRuleForm() {
    const sourceNode = document.getElementById('sourceNode').value;
    const targetNode = document.getElementById('targetNode').value;
    const conditionType = document.getElementById('conditionType').value;
    const priority = document.getElementById('priority').value;
    
    if (!sourceNode) {
        showMessage('请选择来源节点', 'error');
        return false;
    }
    
    if (!targetNode) {
        showMessage('请选择目标节点', 'error');
        return false;
    }
    
    if (sourceNode === targetNode) {
        showMessage('来源节点和目标节点不能相同', 'error');
        return false;
    }
    
    if (!conditionType) {
        showMessage('请选择条件类型', 'error');
        return false;
    }
    
    if (!priority || priority < 1 || priority > 100) {
        showMessage('优先级必须是1-100之间的数字', 'error');
        return false;
    }
    
    // 验证条件值
    const conditionValue = document.getElementById('conditionValue').value;
    if (conditionType !== 'always' && document.getElementById('conditionValue').required && !conditionValue) {
        showMessage('请填写条件值', 'error');
        return false;
    }
    
    if (conditionType === 'timeout' && conditionValue && (isNaN(conditionValue) || conditionValue <= 0)) {
        showMessage('超时时间必须是大于0的数字', 'error');
        return false;
    }
    
    return true;
}

// 加载工作流定义
function loadWorkflowDefinitions() {
    const productLineId = document.getElementById('defProductLineFilter').value;
    
    // 显示加载状态
    document.getElementById('mainFlowContainer').innerHTML = '<div class="loading">加载中...</div>';
    document.getElementById('collabFlowContainer').innerHTML = '<div class="loading">加载中...</div>';
    
    let url = '/api/templates/workflows';
    if (productLineId) {
        url = `/api/templates/product-lines/${productLineId}/workflows`;
    }
    
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                renderWorkflowDefinitions(data.data);
            } else {
                showMessage('加载工作流定义失败: ' + data.message, 'error');
                document.getElementById('mainFlowContainer').innerHTML = '<div class="empty-state">加载失败</div>';
                document.getElementById('collabFlowContainer').innerHTML = '<div class="empty-state">加载失败</div>';
            }
        })
        .catch(error => {
            console.error('加载工作流定义失败:', error);
            showMessage('加载工作流定义失败', 'error');
            document.getElementById('mainFlowContainer').innerHTML = '<div class="empty-state">加载失败</div>';
            document.getElementById('collabFlowContainer').innerHTML = '<div class="empty-state">加载失败</div>';
        });
}

// 渲染工作流定义
function renderWorkflowDefinitions(definitions) {
    const mainFlowContainer = document.getElementById('mainFlowContainer');
    const collabFlowContainer = document.getElementById('collabFlowContainer');
    
    // 分离主流程和协同流程
    const mainFlow = definitions.filter(def => def.flow_type == 1).sort((a, b) => a.order_no - b.order_no);
    const collabFlow = definitions.filter(def => def.flow_type == 2).sort((a, b) => a.order_no - b.order_no);
    
    // 渲染主流程
    if (mainFlow.length > 0) {
        mainFlowContainer.innerHTML = mainFlow.map(def => createWorkflowNodeHtml(def)).join('');
    } else {
        mainFlowContainer.innerHTML = '<div class="empty-state">暂无主流程节点</div>';
    }
    
    // 渲染协同流程
    if (collabFlow.length > 0) {
        collabFlowContainer.innerHTML = collabFlow.map(def => createWorkflowNodeHtml(def)).join('');
    } else {
        collabFlowContainer.innerHTML = '<div class="empty-state">暂无协同流程节点</div>';
    }
}

// 创建工作流节点HTML
function createWorkflowNodeHtml(definition) {
    return `
        <div class="workflow-node" data-id="${definition.id}">
            <div class="node-header">
                <div class="node-title">
                    <i class="fas fa-circle"></i>
                    ${definition.node_name || '未命名节点'}
                </div>
                <div class="node-actions">
                    <button class="btn-icon" onclick="editWorkflowDefinition(${definition.id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon" onclick="deleteWorkflowDefinition(${definition.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="node-info">
                <div class="info-item">
                    <span class="label">节点类型:</span>
                    <span class="value">${definition.node_type || '未知'}</span>
                </div>
                <div class="info-item">
                    <span class="label">排序:</span>
                    <span class="value">${definition.order_no}</span>
                </div>
                <div class="info-item">
                    <span class="label">流程类型:</span>
                    <span class="value">${definition.flow_type === 'main' ? '主流程' : '协同流程'}</span>
                </div>
            </div>
        </div>
    `;
}

// 创建工作流定义
function createWorkflowDef() {
    showWorkflowDefinitionModal();
}

// 显示工作流定义模态框
function showWorkflowDefinitionModal(definition = null) {
    const modal = document.getElementById('workflowDefinitionModal');
    const form = document.getElementById('workflowDefinitionForm');
    const title = document.getElementById('workflowDefinitionModalTitle');
    
    if (definition) {
        title.textContent = '编辑工作流定义';
        // 填充表单数据
        document.getElementById('defProductLineId').value = definition.product_line_id || '';
        document.getElementById('defNodeTemplateId').value = definition.node_template_id || '';
        document.getElementById('defFlowType').value = definition.flow_type || '';
        document.getElementById('defOrderNo').value = definition.order_no || '';
        form.dataset.editId = definition.id;
    } else {
        title.textContent = '新建工作流定义';
        form.reset();
        delete form.dataset.editId;
    }
    
    modal.style.display = 'flex';
    loadProductLinesForDefinition();
    loadNodeTemplatesForDefinition();
}

// 关闭工作流定义模态框
function closeWorkflowDefinitionModal() {
    document.getElementById('workflowDefinitionModal').style.display = 'none';
}

// 编辑工作流定义
function editWorkflowDefinition(id) {
    fetch(`/api/templates/workflows/${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showWorkflowDefinitionModal(data.data);
            } else {
                showMessage('获取工作流定义失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('获取工作流定义失败:', error);
            showMessage('获取工作流定义失败', 'error');
        });
}

// 删除工作流定义
function deleteWorkflowDefinition(id) {
    if (!confirm('确定要删除这个工作流定义吗？')) {
        return;
    }
    
    fetch(`/api/templates/workflows/${id}`, {
        method: 'DELETE'
    })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showMessage('删除工作流定义成功', 'success');
                loadWorkflowDefinitions();
            } else {
                showMessage('删除工作流定义失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('删除工作流定义失败:', error);
            showMessage('删除工作流定义失败', 'error');
        });
}

// 加载产品线数据（用于工作流定义）
function loadProductLinesForDefinition() {
    fetch('/api/v1/master/product-lines')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('defProductLineId');
            select.innerHTML = '<option value="">请选择产品线</option>';
            
            if (data.status === 'success' && data.data) {
                data.data.forEach(line => {
                    const option = document.createElement('option');
                    option.value = line.id;
                    option.textContent = line.name;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('加载产品线失败:', error);
        });
}

// 加载节点模板数据（用于工作流定义）
function loadNodeTemplatesForDefinition() {
    fetch('/api/v1/templates/nodes')
        .then(response => {
            console.log('节点模板API响应状态:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('节点模板API返回数据:', data);
            const select = document.getElementById('defNodeTemplateId');
            select.innerHTML = '<option value="">请选择节点模板</option>';
            
            if (data.status === 'success' && data.data && Array.isArray(data.data)) {
                data.data.forEach(template => {
                    const option = document.createElement('option');
                    option.value = template.id;
                    option.textContent = template.node_name; // 修正字段名
                    select.appendChild(option);
                });
                console.log(`成功加载 ${data.data.length} 个节点模板`);
            } else {
                console.warn('节点模板数据格式异常:', data);
                showMessage('节点模板数据格式异常', 'warning');
            }
        })
        .catch(error => {
            console.error('加载节点模板失败:', error);
            showMessage('加载节点模板失败: ' + error.message, 'error');
        });
}

// 加载模拟流转规则数据
function loadMockFlowRules() {
    flowRules = [
        {
            id: 1,
            from_node_template_id: 1,
            to_node_template_id: 2,
            from_node_name: '项目发起',
            from_node_code: 'main_1',
            to_node_name: '需求评审',
            to_node_code: 'main_2',
            condition_type: 'always',
            condition_value: null,
            priority: 10,
            is_active: '1',
            condition_expression: '项目发起后自动流转到需求评审'
        },
        {
            id: 2,
            from_node_template_id: 2,
            to_node_template_id: 3,
            from_node_name: '需求评审',
            from_node_code: 'main_2',
            to_node_name: '技术评估',
            to_node_code: 'collab_1',
            condition_type: 'approval',
            condition_value: 'approved',
            priority: 10,
            is_active: '1',
            condition_expression: '需求评审通过后流转到技术评估'
        }
    ];
    renderFlowRules();
}

// 流转规则表单提交
document.addEventListener('DOMContentLoaded', function() {
    const flowRuleForm = document.getElementById('flowRuleForm');
    if (flowRuleForm) {
        // 条件类型变化时更新条件值字段
        const conditionTypeSelect = document.getElementById('conditionType');
        if (conditionTypeSelect) {
            conditionTypeSelect.addEventListener('change', updateConditionValueField);
        }
        
        flowRuleForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // 表单验证
            if (!validateFlowRuleForm()) {
                return;
            }
            
            const formData = {
                source_node_id: parseInt(document.getElementById('sourceNode').value),
                target_node_id: parseInt(document.getElementById('targetNode').value),
                condition_type: document.getElementById('conditionType').value,
                condition_value: document.getElementById('conditionValue').value || '',
                priority: parseInt(document.getElementById('priority').value),
                is_active: document.getElementById('isActive').checked
            };
            
            // 显示加载状态
            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = '保存中...';
            submitBtn.disabled = true;
            
            try {
                let response;
                if (currentEditingFlowRule) {
                    response = await fetch(`${API_BASE}/templates/flow-rules/${currentEditingFlowRule}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    });
                } else {
                    response = await fetch(`${API_BASE}/templates/flow-rules`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    });
                }
                
                if (response.ok) {
                    const result = await response.json();
                    closeFlowRuleModal();
                    await loadFlowRules();
                    showMessage(currentEditingFlowRule ? '流转规则更新成功！' : '流转规则创建成功！', 'success');
                } else {
                    const error = await response.json();
                    throw new Error(error.message || '操作失败');
                }
            } catch (error) {
                console.error('保存流转规则失败:', error);
                showMessage('保存流转规则失败: ' + error.message, 'error');
            } finally {
                // 恢复按钮状态
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });
    }
});

// 页面加载完成后执行兼容性修复
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(fixVimiumCompatibility, 100);
    
    // 工作流定义表单提交处理
    const workflowDefinitionForm = document.getElementById('workflowDefinitionForm');
    if (workflowDefinitionForm) {
        workflowDefinitionForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                product_line_id: parseInt(document.getElementById('defProductLineId').value),
                node_template_id: parseInt(document.getElementById('defNodeTemplateId').value),
                flow_type: document.getElementById('defFlowType').value === 'main' ? 1 : 2,
                order_no: parseInt(document.getElementById('defOrderNo').value)
            };
            
            // 暂时不处理is_start_node和is_end_node，这些可以在后续版本中添加
            
            const isEdit = this.dataset.editId;
            const url = isEdit ? `/api/templates/workflows/${this.dataset.editId}` : '/api/templates/workflows';
            const method = isEdit ? 'PUT' : 'POST';
            
            // 显示加载状态
            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = '保存中...';
            submitBtn.disabled = true;
            
            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        showMessage(isEdit ? '更新工作流定义成功' : '创建工作流定义成功', 'success');
                        closeWorkflowDefinitionModal();
                        loadWorkflowDefinitions();
                    } else {
                        showMessage('保存工作流定义失败: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('保存工作流定义失败:', error);
                    showMessage('保存工作流定义失败', 'error');
                })
                .finally(() => {
                    // 恢复按钮状态
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                });
        });
    }
});
</script>
<?= $this->endSection() ?>
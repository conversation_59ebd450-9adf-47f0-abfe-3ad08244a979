<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目流程管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: #f0f2f5;
            color: #2c3e50;
            line-height: 1.6;
        }
        
        .layout {
            min-height: 100vh;
            display: flex;
        }
        
        .sidebar {
            width: 240px;
            background: #001529;
            min-height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
            transition: all 0.3s;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #1c2d42;
        }
        
        .sidebar-header h1 {
            color: #fff;
            font-size: 18px;
            font-weight: 600;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 20px 0;
        }
        
        .sidebar-menu li {
            margin: 4px 12px;
        }
        
        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            color: rgba(255, 255, 255, 0.65);
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.3s;
        }
        
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: #1890ff;
            color: #fff;
        }
        
        .sidebar-menu i {
            margin-right: 12px;
            width: 16px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            margin-left: 240px;
            min-height: 100vh;
        }
        
        .top-bar {
            background: #fff;
            padding: 0 24px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #e8e8e8;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .breadcrumb {
            color: #666;
            font-size: 14px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #1890ff;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }
        
        .content-area {
            padding: 24px;
        }
        
        .page-header {
            margin-bottom: 24px;
        }
        
        .page-title {
            font-size: 20px;
            color: #262626;
            margin-bottom: 8px;
        }
        
        .page-description {
            color: #8c8c8c;
            font-size: 14px;
        }
        
        .card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            margin-bottom: 24px;
        }
        
        .card-header {
            padding: 16px 24px;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
        }
        
        .card-body {
            padding: 24px;
        }
        
        .toolbar {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
            flex-wrap: wrap;
        }
        
        .search-input {
            width: 300px;
            height: 36px;
            padding: 0 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .search-input:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }
        
        .select {
            height: 36px;
            padding: 0 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: #fff;
            font-size: 14px;
            min-width: 120px;
        }
        
        .btn {
            height: 36px;
            padding: 0 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s;
            text-decoration: none;
            color: #fff;
        }
        
        .btn-primary {
            background: #1890ff;
        }
        
        .btn-primary:hover {
            background: #40a9ff;
        }
        
        .btn-success {
            background: #52c41a;
        }
        
        .btn-success:hover {
            background: #73d13d;
        }
        
        .btn-danger {
            background: #ff4d4f;
        }
        
        .btn-danger:hover {
            background: #ff7875;
        }
        
        .btn-default {
            background: #fff;
            border: 1px solid #d9d9d9;
            color: #595959;
        }
        
        .btn-default:hover {
            border-color: #40a9ff;
            color: #40a9ff;
        }
        
        .btn-small {
            height: 28px;
            padding: 0 8px;
            font-size: 12px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            background: #fff;
        }
        
        .table th,
        .table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e8e8e8;
        }
        
        .table th {
            background: #fafafa;
            color: #595959;
            font-weight: 600;
            font-size: 14px;
        }
        
        .table td {
            font-size: 14px;
        }
        
        .table tr:hover {
            background: #fafafa;
        }
        
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            line-height: 20px;
        }
        
        .tag-success {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .tag-processing {
            background: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }
        
        .tag-default {
            background: #fafafa;
            color: #595959;
            border: 1px solid #d9d9d9;
        }
        
        .tag-error {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }
        
        .actions {
            display: flex;
            gap: 8px;
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 16px;
            font-size: 14px;
        }
        
        .alert-success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        
        .alert-error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        
        .alert-info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #8c8c8c;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            color: #d9d9d9;
        }
        
        .empty-title {
            font-size: 16px;
            color: #595959;
            margin-bottom: 8px;
        }
        
        .empty-description {
            font-size: 14px;
            color: #8c8c8c;
        }
        
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.45);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .modal-dialog {
            background: #fff;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            padding: 16px 24px;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .modal-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 16px;
            cursor: pointer;
            color: #8c8c8c;
            padding: 4px;
        }
        
        .modal-body {
            padding: 24px;
        }
        
        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid #e8e8e8;
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 4px;
            font-size: 14px;
            color: #262626;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            height: 36px;
            padding: 0 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .form-control:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }
        
        .form-control[type="textarea"] {
            height: auto;
            padding: 8px 12px;
            resize: vertical;
            min-height: 80px;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .content-area {
                padding: 16px;
            }
            
            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-input {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="layout">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h1><i class="fas fa-project-diagram"></i> 流程管理</h1>
            </div>
            <ul class="sidebar-menu">
                <li><a href="#" class="active" onclick="switchPage('projects')"><i class="fas fa-folder"></i> 项目管理</a></li>
                <li><a href="#" onclick="switchPage('templates')"><i class="fas fa-sitemap"></i> 流程模板</a></li>
                <li><a href="#" onclick="switchPage('workflows')"><i class="fas fa-route"></i> 流程跟踪</a></li>
                <li><a href="#" onclick="switchPage('statistics')"><i class="fas fa-chart-bar"></i> 统计报表</a></li>
                <li><a href="#"><i class="fas fa-users"></i> 用户管理</a></li>
                <li><a href="#"><i class="fas fa-cog"></i> 系统设置</a></li>
            </ul>
        </div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部栏 -->
            <div class="top-bar">
                <div class="breadcrumb">
                    <span id="breadcrumbText">项目管理 / 项目列表</span>
                </div>
                <div class="user-info">
                    <span>管理员</span>
                    <div class="avatar">A</div>
                </div>
            </div>
            
            <!-- 内容区域 -->
            <div class="content-area">
                <div id="apiStatus" class="alert alert-info" style="display: none;">
                    正在连接后端API...
                </div>
                
                <!-- 项目管理页面 -->
                <div id="projects" class="page-content">
                    <div class="page-header">
                        <div class="page-title">项目管理</div>
                        <div class="page-description">管理A、B、C、D四条产品线的项目全流程</div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">项目列表</div>
                            <button class="btn btn-primary" onclick="createProject()">
                                <i class="fas fa-plus"></i> 新建项目
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="toolbar">
                                <input type="text" id="searchInput" class="search-input" placeholder="搜索项目名称或编号..." onkeyup="filterProjects()">
                                <select id="statusFilter" class="select" onchange="filterProjects()">
                                    <option value="">全部状态</option>
                                    <option value="0">草稿</option>
                                    <option value="1">进行中</option>
                                    <option value="2">已完成</option>
                                    <option value="3">已取消</option>
                                </select>
                                <select id="productLineFilter" class="select" onchange="filterProjects()">
                                    <option value="">全部产品线</option>
                                    <option value="A">产品线A</option>
                                    <option value="B">产品线B</option>
                                    <option value="C">产品线C</option>
                                    <option value="D">产品线D</option>
                                </select>
                                <button class="btn btn-default" onclick="exportProjects()">
                                    <i class="fas fa-download"></i> 导出
                                </button>
                            </div>
                            
                            <table class="table" id="projectTable">
                                <thead>
                                    <tr>
                                        <th>项目编号</th>
                                        <th>项目名称</th>
                                        <th>产品线</th>
                                        <th>主部门</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="projectTableBody">
                                    <!-- 项目数据将通过JS动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- 流程模板管理页面 -->
                <div id="templates" class="page-content" style="display: none;">
                    <div class="page-header">
                        <div class="page-title">流程模板管理</div>
                        <div class="page-description">管理各产品线的流程节点模板</div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">节点模板列表</div>
                            <div>
                                <select id="templateProductLine" class="select" onchange="loadTemplates()" style="margin-right: 12px;">
                                    <option value="">选择产品线</option>
                                    <option value="A">产品线A</option>
                                    <option value="B">产品线B</option>
                                    <option value="C">产品线C</option>
                                    <option value="D">产品线D</option>
                                </select>
                                <button class="btn btn-primary" onclick="createTemplate()">
                                    <i class="fas fa-plus"></i> 新建模板
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="toolbar">
                                <input type="text" id="templateSearchInput" class="search-input" placeholder="搜索模板名称或编码..." onkeyup="filterTemplates()">
                                <select id="templateTypeFilter" class="select" onchange="filterTemplates()">
                                    <option value="">全部类型</option>
                                    <option value="main">主流程</option>
                                    <option value="collab">协同流程</option>
                                </select>
                                <button class="btn btn-success" onclick="exportTemplates()">
                                    <i class="fas fa-download"></i> 导出模板
                                </button>
                                <button class="btn btn-default" onclick="importTemplates()">
                                    <i class="fas fa-upload"></i> 导入模板
                                </button>
                            </div>
                            
                            <table class="table" id="templateTable">
                                <thead>
                                    <tr>
                                        <th>节点编码</th>
                                        <th>节点名称</th>
                                        <th>产品线</th>
                                        <th>流程类型</th>
                                        <th>负责角色</th>
                                        <th>排序</th>
                                        <th>可跳过</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="templateTableBody">
                                    <!-- 模板数据将通过JS动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- 流程跟踪页面 -->
                <div id="workflows" class="page-content" style="display: none;">
                    <div class="page-header">
                        <div class="page-title">流程跟踪</div>
                        <div class="page-description">跟踪项目流程执行情况</div>
                    </div>
                    
                    <div class="empty-state">
                        <i class="fas fa-route"></i>
                        <div class="empty-title">流程跟踪</div>
                        <div class="empty-description">此功能正在开发中，敬请期待</div>
                    </div>
                </div>
                
                <!-- 统计报表页面 -->
                <div id="statistics" class="page-content" style="display: none;">
                    <div class="page-header">
                        <div class="page-title">统计报表</div>
                        <div class="page-description">查看项目和流程执行的统计数据</div>
                    </div>
                    
                    <div class="empty-state">
                        <i class="fas fa-chart-bar"></i>
                        <div class="empty-title">统计报表</div>
                        <div class="empty-description">此功能正在开发中，敬请期待</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 模板编辑模态框 -->
    <div id="templateModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <div class="modal-title" id="templateModalTitle">新建模板</div>
                <button class="modal-close" onclick="closeTemplateModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="templateForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label class="form-label">节点编码 <span style="color: #ff4d4f;">*</span></label>
                        <input type="text" id="nodeCode" class="form-control" required placeholder="如: main_1, collab_1">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">节点名称 <span style="color: #ff4d4f;">*</span></label>
                        <input type="text" id="nodeName" class="form-control" required placeholder="如: 项目发起">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">产品线 <span style="color: #ff4d4f;">*</span></label>
                        <select id="nodeProductLine" class="form-control" required>
                            <option value="">请选择产品线</option>
                            <option value="A">产品线A</option>
                            <option value="B">产品线B</option>
                            <option value="C">产品线C</option>
                            <option value="D">产品线D</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">流程类型 <span style="color: #ff4d4f;">*</span></label>
                        <select id="nodeType" class="form-control" required>
                            <option value="">请选择流程类型</option>
                            <option value="main">主流程</option>
                            <option value="collab">协同流程</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">负责角色 <span style="color: #ff4d4f;">*</span></label>
                        <select id="assigneeRole" class="form-control" required>
                            <option value="">请选择角色</option>
                            <option value="1">销售</option>
                            <option value="2">主部门负责人</option>
                            <option value="3">主部门执行人员</option>
                            <option value="4">协同部门负责人</option>
                            <option value="5">协同部门执行人员</option>
                            <option value="6">数据分析师</option>
                            <option value="7">商务负责人</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">节点排序 <span style="color: #ff4d4f;">*</span></label>
                        <input type="number" id="orderNo" class="form-control" required min="1" max="20" placeholder="1-20">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" id="isOptional" style="margin-right: 8px;"> 该节点可跳过
                        </label>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">描述</label>
                        <textarea id="nodeDescription" class="form-control" type="textarea" rows="3" placeholder="节点功能描述（可选）"></textarea>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" onclick="closeTemplateModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- 项目编辑模态框 -->
    <div id="projectModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <div class="modal-title" id="projectModalTitle">新建项目</div>
                <button class="modal-close" onclick="closeProjectModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="projectForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label class="form-label">项目名称 <span style="color: #ff4d4f;">*</span></label>
                        <input type="text" id="projectTitle" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">项目编号 <span style="color: #ff4d4f;">*</span></label>
                        <input type="text" id="projectNo" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">产品线 <span style="color: #ff4d4f;">*</span></label>
                        <select id="projectProductLine" class="form-control" required>
                            <option value="">请选择产品线</option>
                            <option value="A">产品线A</option>
                            <option value="B">产品线B</option>
                            <option value="C">产品线C</option>
                            <option value="D">产品线D</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">项目状态</label>
                        <select id="projectStatus" class="form-control">
                            <option value="0">草稿</option>
                            <option value="1">进行中</option>
                            <option value="2">已完成</option>
                            <option value="3">已取消</option>
                        </select>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" onclick="closeProjectModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // API 基础URL
        const API_BASE = '/api/v1';
        
        // 全局数据
        let projects = [];
        let templates = [];
        let productLines = [];
        let departments = [];
        let roles = [];
        let currentEditingTemplate = null;
        let currentEditingProject = null;
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
        });
        
        // 初始化页面
        async function initializePage() {
            try {
                await checkApiConnection();
                await loadInitialData();
                switchPage('projects'); // 默认显示项目管理页面
            } catch (error) {
                console.error('初始化失败:', error);
                loadMockData();
            }
        }
        
        // 检查API连接
        async function checkApiConnection() {
            try {
                const response = await fetch(`${API_BASE}/master/product-lines`);
                const statusEl = document.getElementById('apiStatus');
                
                if (response.ok) {
                    statusEl.className = 'alert alert-success';
                    statusEl.textContent = '✓ 后端API连接成功';
                    statusEl.style.display = 'block';
                    setTimeout(() => {
                        statusEl.style.display = 'none';
                    }, 3000);
                } else {
                    throw new Error('API连接失败');
                }
            } catch (error) {
                const statusEl = document.getElementById('apiStatus');
                statusEl.className = 'alert alert-error';
                statusEl.textContent = '✗ 后端API连接失败，使用模拟数据';
                statusEl.style.display = 'block';
                console.error('API连接错误:', error);
                throw error;
            }
        }
        
        // 加载初始数据
        async function loadInitialData() {
            await Promise.all([
                loadProductLines(),
                loadDepartments(),
                loadRoles(),
                loadProjects(),
                loadTemplates()
            ]);
        }
        
        // 加载产品线数据
        async function loadProductLines() {
            try {
                const response = await fetch(`${API_BASE}/master/product-lines`);
                if (response.ok) {
                    productLines = await response.json();
                }
            } catch (error) {
                console.error('加载产品线失败:', error);
            }
        }
        
        // 加载部门数据
        async function loadDepartments() {
            try {
                const response = await fetch(`${API_BASE}/master/departments`);
                if (response.ok) {
                    departments = await response.json();
                }
            } catch (error) {
                console.error('加载部门失败:', error);
            }
        }
        
        // 加载角色数据
        async function loadRoles() {
            try {
                const response = await fetch(`${API_BASE}/master/roles`);
                if (response.ok) {
                    roles = await response.json();
                }
            } catch (error) {
                console.error('加载角色失败:', error);
            }
        }
        
        // 加载项目数据
        async function loadProjects() {
            try {
                const response = await fetch(`${API_BASE}/projects`);
                if (response.ok) {
                    projects = await response.json();
                    renderProjects();
                } else {
                    throw new Error('项目数据加载失败');
                }
            } catch (error) {
                console.error('加载项目失败:', error);
                loadMockProjects();
            }
        }
        
        // 加载模板数据
        async function loadTemplates() {
            try {
                const response = await fetch(`${API_BASE}/templates/nodes`);
                if (response.ok) {
                    templates = await response.json();
                    renderTemplates();
                } else {
                    throw new Error('模板数据加载失败');
                }
            } catch (error) {
                console.error('加载模板失败:', error);
                loadMockTemplates();
            }
        }
        
        // 页面切换
        function switchPage(pageName) {
            // 隐藏所有页面内容
            document.querySelectorAll('.page-content').forEach(page => {
                page.style.display = 'none';
            });
            
            // 取消所有菜单项的激活状态
            document.querySelectorAll('.sidebar-menu a').forEach(link => {
                link.classList.remove('active');
            });
            
            // 显示选中的页面内容
            const targetPage = document.getElementById(pageName);
            if (targetPage) {
                targetPage.style.display = 'block';
            }
            
            // 激活选中的菜单项
            if (event && event.target) {
                event.target.classList.add('active');
            }
            
            // 更新面包屑
            updateBreadcrumb(pageName);
        }
        
        // 更新面包屑
        function updateBreadcrumb(pageName) {
            const breadcrumbMap = {
                'projects': '项目管理 / 项目列表',
                'templates': '流程模板 / 模板管理',
                'workflows': '流程跟踪 / 跟踪列表',
                'statistics': '统计报表 / 数据统计'
            };
            
            const breadcrumbEl = document.getElementById('breadcrumbText');
            if (breadcrumbEl && breadcrumbMap[pageName]) {
                breadcrumbEl.textContent = breadcrumbMap[pageName];
            }
        }
        
        // =============模板管理功能=============
        
        // 渲染模板列表
        function renderTemplates() {
            const tableBody = document.getElementById('templateTableBody');
            if (!tableBody) return;
            
            let filteredTemplates = filterTemplateData();
            
            if (filteredTemplates.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 40px; color: #8c8c8c;">
                            <i class="fas fa-inbox" style="font-size: 24px; margin-bottom: 8px; display: block;"></i>
                            暂无模板数据
                        </td>
                    </tr>
                `;
                return;
            }
            
            tableBody.innerHTML = filteredTemplates.map(template => {
                const productLineName = getProductLineName(template.product_line_code);
                const roleName = getRoleName(template.assignee_role_id);
                const typeText = template.flow_type === 'main' ? '主流程' : '协同流程';
                const optionalText = template.is_optional ? '是' : '否';
                
                return `
                    <tr>
                        <td>${template.node_code}</td>
                        <td>${template.node_name}</td>
                        <td>${productLineName}</td>
                        <td>
                            <span class="tag ${template.flow_type === 'main' ? 'tag-processing' : 'tag-success'}">
                                ${typeText}
                            </span>
                        </td>
                        <td>${roleName}</td>
                        <td>${template.order_no}</td>
                        <td>
                            <span class="tag ${template.is_optional ? 'tag-default' : 'tag-error'}">
                                ${optionalText}
                            </span>
                        </td>
                        <td>
                            <div class="actions">
                                <button class="btn btn-default btn-small" onclick="editTemplate(${template.id})">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                                <button class="btn btn-danger btn-small" onclick="deleteTemplate(${template.id})">
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }
        
        // 筛选模板数据
        function filterTemplateData() {
            const searchText = (document.getElementById('templateSearchInput')?.value || '').toLowerCase();
            const typeFilter = document.getElementById('templateTypeFilter')?.value || '';
            const productLineFilter = document.getElementById('templateProductLine')?.value || '';
            
            return templates.filter(template => {
                const matchSearch = !searchText || 
                    template.node_name.toLowerCase().includes(searchText) ||
                    template.node_code.toLowerCase().includes(searchText);
                
                const matchType = !typeFilter || template.flow_type === typeFilter;
                const matchProductLine = !productLineFilter || template.product_line_code === productLineFilter;
                
                return matchSearch && matchType && matchProductLine;
            });
        }
        
        // 过滤模板
        function filterTemplates() {
            renderTemplates();
        }
        
        // 创建模板
        function createTemplate() {
            currentEditingTemplate = null;
            document.getElementById('templateModalTitle').textContent = '新建模板';
            document.getElementById('templateForm').reset();
            document.getElementById('templateModal').style.display = 'flex';
        }
        
        // 编辑模板
        function editTemplate(templateId) {
            const template = templates.find(t => t.id === templateId);
            if (!template) return;
            
            currentEditingTemplate = templateId;
            document.getElementById('templateModalTitle').textContent = '编辑模板';
            
            // 填充表单
            document.getElementById('nodeCode').value = template.node_code;
            document.getElementById('nodeName').value = template.node_name;
            document.getElementById('nodeProductLine').value = template.product_line_code;
            document.getElementById('nodeType').value = template.flow_type;
            document.getElementById('assigneeRole').value = template.assignee_role_id;
            document.getElementById('orderNo').value = template.order_no;
            document.getElementById('isOptional').checked = template.is_optional;
            document.getElementById('nodeDescription').value = template.description || '';
            
            document.getElementById('templateModal').style.display = 'flex';
        }
        
        // 关闭模板模态框
        function closeTemplateModal() {
            document.getElementById('templateModal').style.display = 'none';
            currentEditingTemplate = null;
        }
        
        // 删除模板
        async function deleteTemplate(templateId) {
            if (!confirm('确定要删除这个模板吗？此操作不可撤销。')) return;
            
            try {
                const response = await fetch(`${API_BASE}/templates/nodes/${templateId}`, {
                    method: 'DELETE'
                });
                
                if (response.ok) {
                    templates = templates.filter(t => t.id !== templateId);
                    renderTemplates();
                    showMessage('模板删除成功！', 'success');
                } else {
                    throw new Error('删除失败');
                }
            } catch (error) {
                console.error('删除模板失败:', error);
                showMessage('删除模板失败，请重试', 'error');
            }
        }
        
        // 加载模拟数据
        function loadMockData() {
            productLines = [
                { id: 1, code: "A", name: "产品线A" },
                { id: 2, code: "B", name: "产品线B" },
                { id: 3, code: "C", name: "产品线C" },
                { id: 4, code: "D", name: "产品线D" }
            ];
            
            departments = [
                { id: 1, name: "技术部" },
                { id: 2, name: "运营部" },
                { id: 3, name: "市场部" },
                { id: 4, name: "数据部" }
            ];
            
            loadMockProjects();
        }
        
        // 加载模拟项目数据
        function loadMockProjects() {
            projects = [
                {
                    id: 1,
                    project_no: "PRJ2024001",
                    title: "智能数据分析平台",
                    product_line_code: "A",
                    status: 1,
                    main_department_id: 1,
                    created_at: "2024-01-15"
                },
                {
                    id: 2,
                    project_no: "PRJ2024002", 
                    title: "企业管理系统升级",
                    product_line_code: "B",
                    status: 2,
                    main_department_id: 2,
                    created_at: "2024-01-10"
                },
                {
                    id: 3,
                    project_no: "PRJ2024003",
                    title: "移动端APP开发",
                    product_line_code: "C",
                    status: 1,
                    main_department_id: 1,
                    created_at: "2024-01-20"
                }
            ];
            renderProjects();
        }
        
        // 标签页切换
        function switchTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 取消所有标签页按钮的激活状态
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签页内容
            document.getElementById(tabName).classList.add('active');
            
            // 激活选中的标签页按钮
            event.target.classList.add('active');
        }
        
        // 获取状态显示信息
        function getStatusInfo(status) {
            const statusMap = {
                0: { text: '草稿', class: 'status-draft' },
                1: { text: '进行中', class: 'status-active' },
                2: { text: '已完成', class: 'status-completed' },
                3: { text: '已取消', class: 'status-cancelled' }
            };
            return statusMap[status] || { text: '未知', class: 'status-draft' };
        }
        
        // 获取产品线名称
        function getProductLineName(code) {
            const line = productLines.find(p => p.code === code);
            return line ? line.name : `产品线${code}`;
        }
        
        // 获取部门名称
        function getDepartmentName(id) {
            const dept = departments.find(d => d.id === id);
            return dept ? dept.name : '未分配';
        }
        
        // 渲染项目列表
        function renderProjects() {
            const grid = document.getElementById('projectGrid');
            let filteredProjects = filterProjectData();
            
            if (filteredProjects.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state" style="grid-column: 1/-1;">
                        <div style="font-size: 4rem; margin-bottom: 20px; opacity: 0.3;">📂</div>
                        <h3>暂无项目数据</h3>
                        <p>点击"新建项目"开始创建第一个项目</p>
                    </div>
                `;
                return;
            }
            
            grid.innerHTML = filteredProjects.map(project => {
                const statusInfo = getStatusInfo(project.status);
                const productLineName = getProductLineName(project.product_line_code);
                const departmentName = getDepartmentName(project.main_department_id);
                
                return `
                    <div class="project-card">
                        <div class="project-header">
                            <div>
                                <div class="project-title">${project.title}</div>
                                <div class="project-no">${project.project_no}</div>
                            </div>
                            <span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>
                        </div>
                        
                        <div class="project-info">
                            <div class="info-row">
                                <span class="info-label">产品线:</span>
                                <span class="info-value">${productLineName}</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">主部门:</span>
                                <span class="info-value">${departmentName}</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">创建时间:</span>
                                <span class="info-value">${project.created_at}</span>
                            </div>
                        </div>
                        
                        <div class="project-actions">
                            <button class="btn btn-small" onclick="editProject(${project.id})">编辑</button>
                            <button class="btn btn-warning btn-small" onclick="viewWorkflow(${project.id})">流程</button>
                            <button class="btn btn-danger btn-small" onclick="deleteProject(${project.id})">删除</button>
                        </div>
                    </div>
                `;
            }).join('');
        }
        
        // 筛选项目数据
        function filterProjectData() {
            const searchText = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const productLineFilter = document.getElementById('productLineFilter').value;
            
            return projects.filter(project => {
                const matchSearch = !searchText || 
                    project.title.toLowerCase().includes(searchText) ||
                    project.project_no.toLowerCase().includes(searchText);
                
                const matchStatus = !statusFilter || project.status.toString() === statusFilter;
                const matchProductLine = !productLineFilter || project.product_line_code === productLineFilter;
                
                return matchSearch && matchStatus && matchProductLine;
            });
        }
        
        // 过滤项目
        function filterProjects() {
            renderProjects();
        }
        
        // 新建项目
        function createProject() {
            alert('新建项目功能正在开发中...');
        }
        
        // 编辑项目
        function editProject(projectId) {
            alert('编辑项目功能正在开发中...');
        }
        
        // 查看工作流
        function viewWorkflow(projectId) {
            alert('查看工作流功能正在开发中...');
        }
        
        // 删除项目
        async function deleteProject(projectId) {
            if (confirm('确定要删除这个项目吗？此操作不可撤销。')) {
                try {
                    const response = await fetch(`${API_BASE}/projects/${projectId}`, {
                        method: 'DELETE'
                    });
                    
                    if (response.ok) {
                        projects = projects.filter(p => p.id !== projectId);
                        renderProjects();
                        alert('项目删除成功！');
                    } else {
                        throw new Error('删除失败');
                    }
                } catch (error) {
                    console.error('删除项目失败:', error);
                    alert('删除项目失败，请重试');
                }
            }
        }
    </script>
</body>
</html>
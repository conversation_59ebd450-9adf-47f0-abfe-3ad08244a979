<script>
// 模态框通用功能
document.addEventListener('click', function(e) {
    // 点击模态框背景关闭
    if (e.target.classList.contains('modal')) {
        e.target.style.display = 'none';
    }
});

// ESC键关闭模态框
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            if (modal.style.display === 'flex') {
                modal.style.display = 'none';
            }
        });
    }
});

// 通用AJAX错误处理
function handleAjaxError(error) {
    console.error('AJAX Error:', error);
    if (typeof showMessage === 'function') {
        showMessage('网络请求失败，请检查连接后重试', 'error');
    } else {
        alert('网络请求失败，请检查连接后重试');
    }
}

// 通用消息显示函数
if (typeof showMessage === 'undefined') {
    function showMessage(message, type = 'info') {
        const alertEl = document.createElement('div');
        alertEl.className = `alert alert-${type}`;
        alertEl.style.position = 'fixed';
        alertEl.style.top = '20px';
        alertEl.style.right = '20px';
        alertEl.style.zIndex = '9999';
        alertEl.style.minWidth = '300px';
        alertEl.innerHTML = `<i class="fas fa-info-circle"></i> ${message}`;
        
        document.body.appendChild(alertEl);
        
        setTimeout(() => {
            if (alertEl.parentNode) {
                alertEl.parentNode.removeChild(alertEl);
            }
        }, 3000);
    }
}
</script>
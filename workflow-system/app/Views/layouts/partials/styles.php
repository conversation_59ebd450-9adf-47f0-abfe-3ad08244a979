<style>
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    background: #f0f2f5;
    color: #2c3e50;
    line-height: 1.6;
}

.layout {
    min-height: 100vh;
    display: flex;
}

.sidebar {
    width: 240px;
    background: #001529;
    min-height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
    transition: all 0.3s;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #1c2d42;
}

.sidebar-header h1 {
    color: #fff;
    font-size: 18px;
    font-weight: 600;
}

.sidebar-menu {
    list-style: none;
    padding: 20px 0;
}

.sidebar-menu li {
    margin: 4px 12px;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    color: rgba(255, 255, 255, 0.65);
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.3s;
}

.sidebar-menu a:hover,
.sidebar-menu a.active {
    background: #1890ff;
    color: #fff;
}

.sidebar-menu i {
    margin-right: 12px;
    width: 16px;
    text-align: center;
}

.main-content {
    flex: 1;
    margin-left: 240px;
    min-height: 100vh;
}

.top-bar {
    background: #fff;
    padding: 0 24px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #e8e8e8;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.breadcrumb {
    color: #666;
    font-size: 14px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #1890ff;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.content-area {
    padding: 24px;
}

.page-header {
    margin-bottom: 24px;
}

.page-title {
    font-size: 20px;
    color: #262626;
    margin-bottom: 8px;
}

.page-description {
    color: #8c8c8c;
    font-size: 14px;
}

.card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    margin-bottom: 24px;
}

.card-header {
    padding: 16px 24px;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
}

.card-body {
    padding: 24px;
}

.toolbar {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
    flex-wrap: wrap;
}

.search-input {
    width: 300px;
    height: 36px;
    padding: 0 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s;
}

.search-input:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    outline: none;
}

.select {
    height: 36px;
    padding: 0 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background: #fff;
    font-size: 14px;
    min-width: 120px;
}

.btn {
    height: 36px;
    padding: 0 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s;
    text-decoration: none;
    color: #fff;
}

.btn-primary {
    background: #1890ff;
}

.btn-primary:hover {
    background: #40a9ff;
}

.btn-success {
    background: #52c41a;
}

.btn-success:hover {
    background: #73d13d;
}

.btn-danger {
    background: #ff4d4f;
}

.btn-danger:hover {
    background: #ff7875;
}

.btn-default {
    background: #fff;
    border: 1px solid #d9d9d9;
    color: #595959;
}

.btn-default:hover {
    border-color: #40a9ff;
    color: #40a9ff;
}

.btn-small {
    height: 28px;
    padding: 0 8px;
    font-size: 12px;
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
}

.table th,
.table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #e8e8e8;
}

.table th {
    background: #fafafa;
    color: #595959;
    font-weight: 600;
    font-size: 14px;
}

.table td {
    font-size: 14px;
}

.table tr:hover {
    background: #fafafa;
}

.tag {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 20px;
}

.tag-success {
    background: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.tag-processing {
    background: #e6f7ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
}

.tag-default {
    background: #fafafa;
    color: #595959;
    border: 1px solid #d9d9d9;
}

.tag-error {
    background: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
}

.actions {
    display: flex;
    gap: 8px;
}

.alert {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 14px;
}

.alert-success {
    background: #f6ffed;
    border: 1px solid #b7eb8f;
    color: #52c41a;
}

.alert-error {
    background: #fff2f0;
    border: 1px solid #ffccc7;
    color: #ff4d4f;
}

.alert-info {
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    color: #1890ff;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #8c8c8c;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #d9d9d9;
}

.empty-title {
    font-size: 16px;
    color: #595959;
    margin-bottom: 8px;
}

.empty-description {
    font-size: 14px;
    color: #8c8c8c;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.45);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-dialog {
    background: #fff;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
}

.modal-close {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    color: #8c8c8c;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s;
}

.modal-close:hover {
    background: #f5f5f5;
    color: #595959;
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid #e8e8e8;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

/* 表单样式 */
.form-group {
    margin-bottom: 16px;
}

.form-label {
    display: block;
    margin-bottom: 4px;
    font-size: 14px;
    color: #262626;
    font-weight: 500;
}

.form-control {
    width: 100%;
    height: 36px;
    padding: 0 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s;
    background: #fff;
}

.form-control:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    outline: none;
}

.form-control[type="textarea"] {
    height: auto;
    padding: 8px 12px;
    resize: vertical;
    min-height: 80px;
}

textarea.form-control {
    height: auto;
    padding: 8px 12px;
    resize: vertical;
    min-height: 80px;
    font-family: inherit;
}

select.form-control {
    cursor: pointer;
}

/* 复选框样式 */
input[type="checkbox"] {
    margin-right: 8px;
    cursor: pointer;
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .content-area {
        padding: 16px;
    }
    
    .toolbar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-input {
        width: 100%;
    }
}

/* 标签页样式 */
.nav-tabs {
    display: flex;
    border-bottom: 1px solid #e8e8e8;
    margin-bottom: 0;
}

.nav-tab {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    color: #666;
    font-size: 14px;
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
    display: flex;
    align-items: center;
    gap: 8px;
}

.nav-tab:hover {
    color: #1890ff;
    background-color: #f5f5f5;
}

.nav-tab.active {
    color: #1890ff;
    border-bottom-color: #1890ff;
    background-color: #fff;
}

.tab-content {
    margin-top: 20px;
}

.tab-content.active {
    display: block;
}

/* 工作流定义样式 */
.workflow-definition-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.flow-section {
    margin-bottom: 30px;
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.flow-section h4 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.flow-container {
    min-height: 200px;
    border: 2px dashed #d9d9d9;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    color: #999;
    background: #fafafa;
}

.flow-container:empty::before {
    content: "拖拽节点模板到此处构建工作流";
    font-style: italic;
}

/* 工作流定义样式 */
.workflow-node {
    background: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    margin-bottom: 16px;
    transition: all 0.3s;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.workflow-node:hover {
    border-color: #1890ff;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.node-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.node-title {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #2c3e50;
}

.node-title i {
    margin-right: 8px;
    color: #1890ff;
}

.node-actions {
    display: flex;
    gap: 8px;
}

.btn-icon {
    background: none;
    border: none;
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    color: #666;
    transition: all 0.3s;
}

.btn-icon:hover {
    background: #f0f0f0;
    color: #1890ff;
}

.node-info {
    padding: 16px;
}

.info-item {
    display: flex;
    margin-bottom: 8px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-item .label {
    width: 80px;
    color: #666;
    font-size: 14px;
}

.info-item .value {
    color: #2c3e50;
    font-size: 14px;
    font-weight: 500;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #999;
    font-style: italic;
    background: #fafafa;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    margin: 16px 0;
}

.workflow-section {
    margin-bottom: 24px;
}

.workflow-section h3 {
    margin-bottom: 16px;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.workflow-section h3 i {
    margin-right: 8px;
    color: #1890ff;
}

#mainFlowContainer,
#collabFlowContainer {
    min-height: 120px;
}
</style>
<?php

namespace App\Models;

use CodeIgniter\Model;

class ProjectCollaborationModel extends Model
{
    protected $table            = 'project_collaboration';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'project_id', 'department_id', 'manager_uid', 'executor_uid', 'analyst_uid',
        'status', 'current_node_template_id',
        'created_at', 'creator', 'updated_at', 'updater', 'deleted_at', 'deleter'
    ];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'project_id' => 'required|integer',
        'department_id' => 'required|integer',
        'status' => 'in_list[0,1,2,3]',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['setCreatedAt'];
    protected $beforeUpdate   = ['setUpdatedAt'];

    protected function setCreatedAt(array $data)
    {
        if (!isset($data['data']['created_at'])) {
            $data['data']['created_at'] = time();
        }
        return $data;
    }

    protected function setUpdatedAt(array $data)
    {
        $data['data']['updated_at'] = time();
        return $data;
    }

    /**
     * 获取项目的协同部门列表
     */
    public function getProjectCollaborations($projectId)
    {
        return $this->select('project_collaboration.*, 
                            department.name as department_name,
                            manager.name as manager_name,
                            executor.name as executor_name,
                            analyst.name as analyst_name,
                            node_template.node_name as current_node_name')
                    ->join('department', 'department.id = project_collaboration.department_id', 'left')
                    ->join('user as manager', 'manager.user_uid = project_collaboration.manager_uid', 'left')
                    ->join('user as executor', 'executor.user_uid = project_collaboration.executor_uid', 'left')
                    ->join('user as analyst', 'analyst.user_uid = project_collaboration.analyst_uid', 'left')
                    ->join('node_template', 'node_template.id = project_collaboration.current_node_template_id', 'left')
                    ->where(['project_collaboration.project_id' => $projectId, 'project_collaboration.deleted_at' => 0])
                    ->orderBy('project_collaboration.created_at', 'ASC')
                    ->findAll();
    }

    /**
     * 更新协同当前节点
     */
    public function updateCurrentNode($collaborationId, $nodeTemplateId, $updaterUid)
    {
        return $this->update($collaborationId, [
            'current_node_template_id' => $nodeTemplateId,
            'updater' => $updaterUid
        ]);
    }

    /**
     * 更新协同状态
     */
    public function updateCollaborationStatus($collaborationId, $status, $updaterUid)
    {
        return $this->update($collaborationId, [
            'status' => $status,
            'updater' => $updaterUid
        ]);
    }
}
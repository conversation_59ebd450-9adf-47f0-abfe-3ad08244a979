<?php

namespace App\Models;

use CodeIgniter\Model;

class NodeFlowRuleModel extends Model
{
    protected $table            = 'node_flow_rule';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'product_line_id', 'from_node_template_id', 'to_node_template_id', 
        'condition_type', 'condition_value', 'condition_expression', 'is_active', 'priority',
        'created_at', 'creator', 'updated_at', 'updater', 'deleted_at', 'deleter'
    ];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'from_node_template_id' => 'required|integer',
        'to_node_template_id' => 'required|integer',
        'condition_type' => 'required|max_length[50]',
        'priority' => 'required|integer'
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['setCreatedAt'];
    protected $beforeUpdate   = ['setUpdatedAt'];

    protected function setCreatedAt(array $data)
    {
        if (!isset($data['data']['created_at'])) {
            $data['data']['created_at'] = time();
        }
        return $data;
    }

    protected function setUpdatedAt(array $data)
    {
        $data['data']['updated_at'] = time();
        return $data;
    }

    /**
     * 获取所有流转规则及节点名称
     */
    public function getAllWithNodeNames()
    {
        return $this->select('node_flow_rule.*, 
                            from_node.node_name as from_node_name,
                            from_node.node_code as from_node_code,
                            to_node.node_name as to_node_name,
                            to_node.node_code as to_node_code')
                    ->join('node_template as from_node', 'from_node.id = node_flow_rule.from_node_template_id', 'left')
                    ->join('node_template as to_node', 'to_node.id = node_flow_rule.to_node_template_id', 'left')
                    ->where('node_flow_rule.deleted_at', 0)
                    ->orderBy('node_flow_rule.priority, node_flow_rule.id')
                    ->findAll();
    }

    /**
     * 获取流转规则
     */
    public function getFlowRules($fromNodeTemplateId)
    {
        return $this->select('node_flow_rule.*, 
                            from_node.node_name as from_node_name,
                            to_node.node_name as to_node_name')
                    ->join('node_template as from_node', 'from_node.id = node_flow_rule.from_node_template_id', 'left')
                    ->join('node_template as to_node', 'to_node.id = node_flow_rule.to_node_template_id', 'left')
                    ->where([
                        'node_flow_rule.from_node_template_id' => $fromNodeTemplateId,
                        'node_flow_rule.is_active' => 1,
                        'node_flow_rule.deleted_at' => 0
                    ])
                    ->orderBy('node_flow_rule.priority', 'ASC')
                    ->findAll();
    }

    /**
     * 获取默认流转规则
     */
    public function getDefaultFlowRule($fromNodeTemplateId)
    {
        return $this->where([
            'from_node_template_id' => $fromNodeTemplateId,
            'condition_type' => 'default',
            'is_active' => 1,
            'deleted_at' => 0
        ])->first();
    }

    /**
     * 获取产品线的所有流转规则
     */
    public function getProductLineFlowRules($productLineId)
    {
        return $this->select('node_flow_rule.*, 
                            from_node.node_name as from_node_name,
                            to_node.node_name as to_node_name')
                    ->join('node_template as from_node', 'from_node.id = node_flow_rule.from_node_template_id', 'left')
                    ->join('node_template as to_node', 'to_node.id = node_flow_rule.to_node_template_id', 'left')
                    ->where([
                        'node_flow_rule.product_line_id' => $productLineId,
                        'node_flow_rule.deleted_at' => 0
                    ])
                    ->orderBy('node_flow_rule.from_node_template_id, node_flow_rule.priority')
                    ->findAll();
    }


}
<?php

namespace App\Models;

use CodeIgniter\Model;

class ProjectModel extends Model
{
    protected $table            = 'project';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'project_no', 'title', 'product_line_id', 'sales_owner_uid', 'status', 'sign_status',
        'main_department_id', 'main_department_manager_uid', 'main_executor_uid', 'current_node_template_id',
        'created_at', 'creator', 'updated_at', 'updater', 'deleted_at', 'deleter'
    ];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'project_no' => 'required|max_length[40]|is_unique[project.project_no]',
        'title' => 'required|max_length[100]',
        'product_line_id' => 'required|integer',
        'sales_owner_uid' => 'required|max_length[16]',
        'status' => 'in_list[0,1,2,3]',
        'sign_status' => 'in_list[0,1,2]',
    ];
    protected $validationMessages   = [
        'project_no' => [
            'required' => '项目编号必填',
            'max_length' => '项目编号最多40个字符',
            'is_unique' => '项目编号已存在'
        ],
        'title' => [
            'required' => '项目标题必填',
            'max_length' => '项目标题最多100个字符'
        ]
    ];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['setCreatedAt', 'generateProjectNo'];
    protected $beforeUpdate   = ['setUpdatedAt'];

    protected function setCreatedAt(array $data)
    {
        if (!isset($data['data']['created_at'])) {
            $data['data']['created_at'] = time();
        }
        return $data;
    }

    protected function setUpdatedAt(array $data)
    {
        $data['data']['updated_at'] = time();
        return $data;
    }

    protected function generateProjectNo(array $data)
    {
        if (empty($data['data']['project_no'])) {
            $data['data']['project_no'] = 'P' . date('YmdHis') . sprintf('%04d', mt_rand(1, 9999));
        }
        return $data;
    }

    /**
     * 获取项目详细信息（包含关联信息）
     */
    public function getProjectWithDetails($projectId)
    {
        return $this->select('project.*, 
                            product_line.name as product_line_name,
                            product_line.code as product_line_code,
                            department.name as main_department_name,
                            node_template.node_name as current_node_name,
                            node_template.node_code as current_node_code')
                    ->join('product_line', 'product_line.id = project.product_line_id', 'left')
                    ->join('department', 'department.id = project.main_department_id', 'left')
                    ->join('node_template', 'node_template.id = project.current_node_template_id', 'left')
                    ->where(['project.id' => $projectId, 'project.deleted_at' => 0])
                    ->first();
    }

    /**
     * 根据项目编号获取项目
     */
    public function getProjectByNo($projectNo)
    {
        return $this->where(['project_no' => $projectNo, 'deleted_at' => 0])->first();
    }

    /**
     * 获取用户的项目列表
     */
    public function getProjectsByUser($userUid, $status = null)
    {
        $where = [
            '(sales_owner_uid = "' . $userUid . '" OR main_department_manager_uid = "' . $userUid . '" OR main_executor_uid = "' . $userUid . '")',
            'deleted_at' => 0
        ];
        
        if ($status !== null) {
            $where['status'] = $status;
        }
        
        return $this->select('project.*, 
                            product_line.name as product_line_name,
                            department.name as main_department_name,
                            node_template.node_name as current_node_name')
                    ->join('product_line', 'product_line.id = project.product_line_id', 'left')
                    ->join('department', 'department.id = project.main_department_id', 'left')
                    ->join('node_template', 'node_template.id = project.current_node_template_id', 'left')
                    ->where($where)
                    ->orderBy('project.created_at DESC')
                    ->findAll();
    }

    /**
     * 更新项目当前节点
     */
    public function updateCurrentNode($projectId, $nodeTemplateId, $updaterUid)
    {
        return $this->update($projectId, [
            'current_node_template_id' => $nodeTemplateId,
            'updater' => $updaterUid
        ]);
    }

    /**
     * 更新项目签约状态
     */
    public function updateSignStatus($projectId, $signStatus, $updaterUid)
    {
        return $this->update($projectId, [
            'sign_status' => $signStatus,
            'updater' => $updaterUid
        ]);
    }

    /**
     * 更新项目状态
     */
    public function updateProjectStatus($projectId, $status, $updaterUid)
    {
        return $this->update($projectId, [
            'status' => $status,
            'updater' => $updaterUid
        ]);
    }
}
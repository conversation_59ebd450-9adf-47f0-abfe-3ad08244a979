<?php

namespace App\Models;

use CodeIgniter\Model;

class UserModel extends Model
{
    protected $table            = 'user';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'user_uid', 'name', 'email', 'department_id', 'role_id',
        'created_at', 'creator', 'updated_at', 'updater', 'deleted_at', 'deleter'
    ];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'user_uid' => 'required|max_length[16]|is_unique[user.user_uid]',
        'name' => 'required|max_length[50]',
        'email' => 'required|valid_email|max_length[100]',
        'department_id' => 'required|integer',
        'role_id' => 'required|integer',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['setCreatedAt'];
    protected $beforeUpdate   = ['setUpdatedAt'];

    protected function setCreatedAt(array $data)
    {
        if (!isset($data['data']['created_at'])) {
            $data['data']['created_at'] = time();
        }
        return $data;
    }

    protected function setUpdatedAt(array $data)
    {
        $data['data']['updated_at'] = time();
        return $data;
    }

    /**
     * 根据用户UID获取用户信息
     */
    public function getUserByUid($userUid)
    {
        return $this->select('user.*, department.name as department_name, role.name as role_name')
                    ->join('department', 'department.id = user.department_id', 'left')
                    ->join('role', 'role.id = user.role_id', 'left')
                    ->where(['user.user_uid' => $userUid, 'user.deleted_at' => 0])
                    ->first();
    }

    /**
     * 根据部门获取部门负责人
     */
    public function getDepartmentManager($departmentId)
    {
        // 假设部门负责人角色ID为2（需要根据实际情况调整）
        return $this->where([
            'department_id' => $departmentId,
            'role_id' => 2, // 部门负责人角色
            'deleted_at' => 0
        ])->first();
    }

    /**
     * 根据部门和角色获取用户列表
     */
    public function getUsersByDepartmentAndRole($departmentId, $roleId)
    {
        return $this->select('user.*, department.name as department_name, role.name as role_name')
                    ->join('department', 'department.id = user.department_id', 'left')
                    ->join('role', 'role.id = user.role_id', 'left')
                    ->where([
                        'user.department_id' => $departmentId,
                        'user.role_id' => $roleId,
                        'user.deleted_at' => 0
                    ])
                    ->findAll();
    }

    /**
     * 获取所有销售人员
     */
    public function getSalesPersons()
    {
        // 假设销售角色ID为1
        return $this->getUsersByRole(1);
    }

    /**
     * 根据角色获取用户列表
     */
    public function getUsersByRole($roleId)
    {
        return $this->select('user.*, department.name as department_name, role.name as role_name')
                    ->join('department', 'department.id = user.department_id', 'left')
                    ->join('role', 'role.id = user.role_id', 'left')
                    ->where(['user.role_id' => $roleId, 'user.deleted_at' => 0])
                    ->orderBy('user.name', 'ASC')
                    ->findAll();
    }
}
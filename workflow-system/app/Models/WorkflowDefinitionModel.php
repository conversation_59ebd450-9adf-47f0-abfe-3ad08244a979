<?php

namespace App\Models;

use CodeIgniter\Model;

class WorkflowDefinitionModel extends Model
{
    protected $table            = 'workflow_definition';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'product_line_id', 'node_template_id', 'flow_type', 'order_no', 'is_start_node', 'is_end_node',
        'created_at', 'creator', 'updated_at', 'updater', 'deleted_at', 'deleter'
    ];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'product_line_id' => 'required|integer',
        'node_template_id' => 'required|integer',
        'flow_type' => 'required|in_list[1,2]',
        'order_no' => 'required|integer',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['setCreatedAt'];
    protected $beforeUpdate   = ['setUpdatedAt'];

    protected function setCreatedAt(array $data)
    {
        if (!isset($data['data']['created_at'])) {
            $data['data']['created_at'] = time();
        }
        return $data;
    }

    protected function setUpdatedAt(array $data)
    {
        $data['data']['updated_at'] = time();
        return $data;
    }

    /**
     * 获取产品线的工作流定义
     */
    public function getProductLineFlow($productLineId, $flowType)
    {
        return $this->select('workflow_definition.*, 
                            node_template.node_code,
                            node_template.node_name,
                            node_template.assignee_role_id,
                            node_template.is_optional,
                            node_template.action_config,
                            role.name as assignee_role_name')
                    ->join('node_template', 'node_template.id = workflow_definition.node_template_id', 'left')
                    ->join('role', 'role.id = node_template.assignee_role_id', 'left')
                    ->where([
                        'workflow_definition.product_line_id' => $productLineId,
                        'workflow_definition.flow_type' => $flowType,
                        'workflow_definition.deleted_at' => 0
                    ])
                    ->orderBy('workflow_definition.order_no', 'ASC')
                    ->findAll();
    }

    /**
     * 获取产品线的主流程
     */
    public function getMainFlow($productLineId)
    {
        return $this->getProductLineFlow($productLineId, 1);
    }

    /**
     * 获取产品线的协同流程
     */
    public function getCollaborationFlow($productLineId)
    {
        return $this->getProductLineFlow($productLineId, 2);
    }

    /**
     * 获取产品线的起始节点
     */
    public function getStartNodeByProductLine($productLineId, $flowType)
    {
        return $this->select('workflow_definition.*, 
                            node_template.node_code,
                            node_template.node_name')
                    ->join('node_template', 'node_template.id = workflow_definition.node_template_id', 'left')
                    ->where([
                        'workflow_definition.product_line_id' => $productLineId,
                        'workflow_definition.flow_type' => $flowType,
                        'workflow_definition.is_start_node' => 1,
                        'workflow_definition.deleted_at' => 0
                    ])
                    ->first();
    }

    /**
     * 获取产品线的结束节点
     */
    public function getEndNodeByProductLine($productLineId, $flowType)
    {
        return $this->select('workflow_definition.*, 
                            node_template.node_code,
                            node_template.node_name')
                    ->join('node_template', 'node_template.id = workflow_definition.node_template_id', 'left')
                    ->where([
                        'workflow_definition.product_line_id' => $productLineId,
                        'workflow_definition.flow_type' => $flowType,
                        'workflow_definition.is_end_node' => 1,
                        'workflow_definition.deleted_at' => 0
                    ])
                    ->first();
    }

    /**
     * 获取下一个节点
     */
    public function getNextNode($productLineId, $currentNodeTemplateId, $flowType)
    {
        // 先获取当前节点的order_no
        $currentNode = $this->where([
            'product_line_id' => $productLineId,
            'node_template_id' => $currentNodeTemplateId,
            'flow_type' => $flowType,
            'deleted_at' => 0
        ])->first();

        if (!$currentNode) {
            return null;
        }

        // 获取下一个order_no的节点
        return $this->select('workflow_definition.*, 
                            node_template.node_code,
                            node_template.node_name')
                    ->join('node_template', 'node_template.id = workflow_definition.node_template_id', 'left')
                    ->where([
                        'workflow_definition.product_line_id' => $productLineId,
                        'workflow_definition.flow_type' => $flowType,
                        'workflow_definition.order_no >' => $currentNode['order_no'],
                        'workflow_definition.deleted_at' => 0
                    ])
                    ->orderBy('workflow_definition.order_no', 'ASC')
                    ->first();
    }

    /**
     * 获取协同流程模板（按产品线）
     */
    public function getCollaborationFlowByProductLine($productLineId)
    {
        return $this->getCollaborationFlow($productLineId);
    }

    /**
     * 检查节点是否为结束节点
     */
    public function isEndNode($productLineId, $nodeTemplateId, $flowType)
    {
        $node = $this->where([
            'product_line_id' => $productLineId,
            'node_template_id' => $nodeTemplateId,
            'flow_type' => $flowType,
            'deleted_at' => 0
        ])->first();

        return $node && $node['is_end_node'] == 1;
    }

    /**
     * 批量删除产品线的工作流定义
     */
    public function deleteProductLineFlow($productLineId, $deleterUid)
    {
        return $this->where('product_line_id', $productLineId)
                    ->set([
                        'deleted_at' => time(),
                        'deleter' => $deleterUid
                    ])
                    ->update();
    }
}
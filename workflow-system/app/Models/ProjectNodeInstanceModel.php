<?php

namespace App\Models;

use CodeIgniter\Model;

class ProjectNodeInstanceModel extends Model
{
    protected $table            = 'project_node_instance';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'project_id', 'collaboration_id', 'node_template_id', 'assignee_uid', 'status',
        'started_at', 'finished_at', 'result_data', 'version',
        'created_at', 'creator', 'updated_at', 'updater', 'deleted_at', 'deleter'
    ];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'project_id' => 'required|integer',
        'collaboration_id' => 'integer',
        'node_template_id' => 'required|integer',
        'status' => 'in_list[0,1,2,3,4]',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['setCreatedAt'];
    protected $beforeUpdate   = ['setUpdatedAt'];

    protected function setCreatedAt(array $data)
    {
        if (!isset($data['data']['created_at'])) {
            $data['data']['created_at'] = time();
        }
        return $data;
    }

    protected function setUpdatedAt(array $data)
    {
        $data['data']['updated_at'] = time();
        return $data;
    }

    /**
     * 获取节点实例
     */
    public function getNodeInstance($projectId, $nodeTemplateId, $collaborationId = 0)
    {
        return $this->where([
            'project_id' => $projectId,
            'node_template_id' => $nodeTemplateId,
            'collaboration_id' => $collaborationId,
            'deleted_at' => 0
        ])->first();
    }

    /**
     * 获取项目的所有节点实例
     */
    public function getProjectNodes($projectId, $collaborationId = 0)
    {
        return $this->select('project_node_instance.*, 
                            node_template.node_code,
                            node_template.node_name,
                            node_template.node_type,
                            user.name as assignee_name')
                    ->join('node_template', 'node_template.id = project_node_instance.node_template_id', 'left')
                    ->join('user', 'user.user_uid = project_node_instance.assignee_uid', 'left')
                    ->where([
                        'project_node_instance.project_id' => $projectId,
                        'project_node_instance.collaboration_id' => $collaborationId,
                        'project_node_instance.deleted_at' => 0
                    ])
                    ->orderBy('project_node_instance.created_at', 'ASC')
                    ->findAll();
    }

    /**
     * 启动节点实例
     */
    public function startNodeInstance($instanceId, $assigneeUid, $updaterUid)
    {
        return $this->update($instanceId, [
            'status' => 1, // 进行中
            'assignee_uid' => $assigneeUid,
            'started_at' => time(),
            'updater' => $updaterUid
        ]);
    }

    /**
     * 完成节点实例
     */
    public function completeNodeInstance($instanceId, $resultData, $updaterUid)
    {
        return $this->update($instanceId, [
            'status' => 2, // 已完成
            'finished_at' => time(),
            'result_data' => json_encode($resultData),
            'updater' => $updaterUid
        ]);
    }

    /**
     * 驳回节点实例
     */
    public function rejectNodeInstance($instanceId, $rejectReason, $updaterUid)
    {
        return $this->update($instanceId, [
            'status' => 3, // 已驳回
            'result_data' => json_encode(['reject_reason' => $rejectReason]),
            'updater' => $updaterUid
        ]);
    }

    /**
     * 重置节点实例状态
     */
    public function resetNodeInstance($instanceId, $updaterUid)
    {
        return $this->update($instanceId, [
            'status' => 0, // 未开始
            'assignee_uid' => '',
            'started_at' => 0,
            'finished_at' => 0,
            'result_data' => null,
            'updater' => $updaterUid
        ]);
    }

    /**
     * 重置指定节点之后的所有节点
     */
    public function resetNodeInstancesAfter($projectId, $collaborationId, $fromNodeTemplateId, $updaterUid)
    {
        // 获取从指定节点开始的所有后续节点
        $instances = $this->where([
            'project_id' => $projectId,
            'collaboration_id' => $collaborationId,
            'deleted_at' => 0
        ])->findAll();

        $resetCount = 0;
        foreach ($instances as $instance) {
            if ($instance['node_template_id'] >= $fromNodeTemplateId) {
                $this->resetNodeInstance($instance['id'], $updaterUid);
                $resetCount++;
            }
        }

        return $resetCount;
    }

    /**
     * 获取节点历史
     */
    public function getNodeHistory($projectId, $nodeTemplateId, $collaborationId = 0)
    {
        return $this->select('project_node_instance.*, 
                            node_template.node_name,
                            user.name as assignee_name')
                    ->join('node_template', 'node_template.id = project_node_instance.node_template_id', 'left')
                    ->join('user', 'user.user_uid = project_node_instance.assignee_uid', 'left')
                    ->where([
                        'project_node_instance.project_id' => $projectId,
                        'project_node_instance.node_template_id' => $nodeTemplateId,
                        'project_node_instance.collaboration_id' => $collaborationId,
                        'project_node_instance.deleted_at' => 0
                    ])
                    ->orderBy('project_node_instance.version', 'DESC')
                    ->findAll();
    }

    /**
     * 更新节点处理人
     */
    public function updateAssignee($instanceId, $assigneeUid, $updaterUid)
    {
        return $this->update($instanceId, [
            'assignee_uid' => $assigneeUid,
            'updater' => $updaterUid
        ]);
    }
}
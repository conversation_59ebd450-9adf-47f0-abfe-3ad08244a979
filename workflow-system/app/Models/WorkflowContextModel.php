<?php

namespace App\Models;

use CodeIgniter\Model;

class WorkflowContextModel extends Model
{
    protected $table            = 'workflow_context';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'project_id', 'collaboration_id', 'context_key', 'context_value', 'data_type',
        'created_at', 'creator', 'updated_at', 'updater', 'deleted_at', 'deleter'
    ];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'project_id' => 'required|integer',
        'collaboration_id' => 'integer',
        'context_key' => 'required|max_length[50]',
        'data_type' => 'in_list[string,int,json]',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['setCreatedAt'];
    protected $beforeUpdate   = ['setUpdatedAt'];

    protected function setCreatedAt(array $data)
    {
        if (!isset($data['data']['created_at'])) {
            $data['data']['created_at'] = time();
        }
        return $data;
    }

    protected function setUpdatedAt(array $data)
    {
        $data['data']['updated_at'] = time();
        return $data;
    }

    /**
     * 获取项目的上下文变量
     */
    public function getContextByProject($projectId, $collaborationId = 0)
    {
        $contexts = $this->where([
            'project_id' => $projectId,
            'collaboration_id' => $collaborationId,
            'deleted_at' => 0
        ])->findAll();

        $result = [];
        foreach ($contexts as $context) {
            $value = $context['context_value'];
            if ($context['data_type'] === 'int') {
                $value = (int)$value;
            } elseif ($context['data_type'] === 'json') {
                $value = json_decode($value, true);
            }
            $result[$context['context_key']] = $value;
        }

        return $result;
    }

    /**
     * 设置上下文变量
     */
    public function setContext($projectId, $collaborationId, $key, $value, $dataType = 'string', $setterUid)
    {
        // 检查是否已存在
        $existing = $this->where([
            'project_id' => $projectId,
            'collaboration_id' => $collaborationId,
            'context_key' => $key,
            'deleted_at' => 0
        ])->first();

        $contextValue = $value;
        if ($dataType === 'json') {
            $contextValue = json_encode($value);
        } elseif ($dataType === 'int') {
            $contextValue = (string)$value;
        }

        $data = [
            'project_id' => $projectId,
            'collaboration_id' => $collaborationId,
            'context_key' => $key,
            'context_value' => $contextValue,
            'data_type' => $dataType,
            'updater' => $setterUid
        ];

        if ($existing) {
            // 更新
            return $this->update($existing['id'], $data);
        } else {
            // 新增
            $data['creator'] = $setterUid;
            return $this->insert($data);
        }
    }

    /**
     * 获取特定上下文值
     */
    public function getContextValue($projectId, $collaborationId, $key)
    {
        $context = $this->where([
            'project_id' => $projectId,
            'collaboration_id' => $collaborationId,
            'context_key' => $key,
            'deleted_at' => 0
        ])->first();

        if (!$context) {
            return null;
        }

        $value = $context['context_value'];
        if ($context['data_type'] === 'int') {
            return (int)$value;
        } elseif ($context['data_type'] === 'json') {
            return json_decode($value, true);
        }

        return $value;
    }

    /**
     * 删除上下文变量
     */
    public function deleteContext($projectId, $collaborationId, $key, $deleterUid)
    {
        return $this->where([
            'project_id' => $projectId,
            'collaboration_id' => $collaborationId,
            'context_key' => $key
        ])->set([
            'deleted_at' => time(),
            'deleter' => $deleterUid
        ])->update();
    }
}
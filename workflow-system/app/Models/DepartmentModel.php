<?php

namespace App\Models;

use CodeIgniter\Model;

class DepartmentModel extends Model
{
    protected $table            = 'department';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'name', 'is_main_dept', 'is_collab_dept',
        'created_at', 'creator', 'updated_at', 'updater', 'deleted_at', 'deleter'
    ];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'name' => 'required|max_length[50]',
    ];
    protected $validationMessages   = [
        'name' => [
            'required' => '部门名称必填',
            'max_length' => '部门名称最多50个字符'
        ]
    ];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['setCreatedAt'];
    protected $beforeUpdate   = ['setUpdatedAt'];

    protected function setCreatedAt(array $data)
    {
        if (!isset($data['data']['created_at'])) {
            $data['data']['created_at'] = time();
        }
        return $data;
    }

    protected function setUpdatedAt(array $data)
    {
        $data['data']['updated_at'] = time();
        return $data;
    }

    /**
     * 获取所有主部门
     */
    public function getMainDepartments()
    {
        return $this->where(['is_main_dept' => 1, 'deleted_at' => 0])
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    /**
     * 获取所有协同部门
     */
    public function getCollaborationDepartments()
    {
        return $this->where(['is_collab_dept' => 1, 'deleted_at' => 0])
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    /**
     * 根据产品线获取主部门（简化版，实际可能需要更复杂的映射关系）
     */
    public function getMainDepartmentByProductLine($productLineCode)
    {
        // 简化的映射关系，实际应该配置在数据库中
        $mapping = [
            'A' => '技术部',
            'B' => '运营部', 
            'C' => '市场部',
            'D' => null // D产品线需要手动选择
        ];

        if (!isset($mapping[$productLineCode])) {
            return null;
        }

        if ($mapping[$productLineCode] === null) {
            return null; // 需要手动选择
        }

        return $this->where([
            'name' => $mapping[$productLineCode],
            'is_main_dept' => 1,
            'deleted_at' => 0
        ])->first();
    }

    /**
     * 获取所有有效部门
     */
    public function getAllActive()
    {
        return $this->where('deleted_at', 0)
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }
}
<?php

namespace App\Models;

use CodeIgniter\Model;

class ProjectActionLogModel extends Model
{
    protected $table            = 'project_action_log';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'project_id', 'collaboration_id', 'node_template_id', 'action_type',
        'action_by_uid', 'action_data', 'remark',
        'created_at', 'creator', 'updated_at', 'updater', 'deleted_at', 'deleter'
    ];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'project_id' => 'required|integer',
        'collaboration_id' => 'integer',
        'node_template_id' => 'integer',
        'action_type' => 'required|in_list[1,2,3,4,5,6,7,8]',
        'action_by_uid' => 'required|max_length[16]',
        'remark' => 'max_length[500]',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['setCreatedAt'];
    protected $beforeUpdate   = ['setUpdatedAt'];

    protected function setCreatedAt(array $data)
    {
        if (!isset($data['data']['created_at'])) {
            $data['data']['created_at'] = time();
        }
        return $data;
    }

    protected function setUpdatedAt(array $data)
    {
        $data['data']['updated_at'] = time();
        return $data;
    }

    /**
     * 获取项目的操作日志
     */
    public function getProjectActionLogs($projectId, $collaborationId = null, $limit = 50, $offset = 0)
    {
        $builder = $this->select('project_action_log.*,
                                node_template.node_name,
                                user.name as action_by_name')
                        ->join('node_template', 'node_template.id = project_action_log.node_template_id', 'left')
                        ->join('user', 'user.user_uid = project_action_log.action_by_uid', 'left')
                        ->where(['project_action_log.project_id' => $projectId, 'project_action_log.deleted_at' => 0]);

        if ($collaborationId !== null) {
            $builder->where('project_action_log.collaboration_id', $collaborationId);
        }

        return $builder->orderBy('project_action_log.created_at', 'DESC')
                      ->limit($limit, $offset)
                      ->findAll();
    }

    /**
     * 获取操作类型文本
     */
    public function getActionTypeText($actionType)
    {
        $types = [
            1 => '提交',
            2 => '审核通过',
            3 => '审核驳回',
            4 => '更新',
            5 => '选择',
            6 => '上传',
            7 => '交付',
            8 => '邮件'
        ];

        return $types[$actionType] ?? '未知操作';
    }
}
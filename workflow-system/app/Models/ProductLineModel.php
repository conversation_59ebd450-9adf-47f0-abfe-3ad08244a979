<?php

namespace App\Models;

use CodeIgniter\Model;

class ProductLineModel extends Model
{
    protected $table            = 'product_line';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'code', 'name', 'created_at', 'creator', 'updated_at', 'updater', 'deleted_at', 'deleter'
    ];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'code' => 'required|max_length[1]|is_unique[product_line.code]',
        'name' => 'required|max_length[30]',
    ];
    protected $validationMessages   = [
        'code' => [
            'required' => '产品线编码必填',
            'max_length' => '产品线编码最多1个字符',
            'is_unique' => '产品线编码已存在'
        ],
        'name' => [
            'required' => '产品线名称必填',
            'max_length' => '产品线名称最多30个字符'
        ]
    ];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['setCreatedAt'];
    protected $beforeUpdate   = ['setUpdatedAt'];

    protected function setCreatedAt(array $data)
    {
        if (!isset($data['data']['created_at'])) {
            $data['data']['created_at'] = time();
        }
        return $data;
    }

    protected function setUpdatedAt(array $data)
    {
        $data['data']['updated_at'] = time();
        return $data;
    }

    /**
     * 根据编码获取产品线
     */
    public function getByCode($code)
    {
        return $this->where(['code' => $code, 'deleted_at' => 0])->first();
    }

    /**
     * 获取所有有效的产品线
     */
    public function getAllActive()
    {
        return $this->where('deleted_at', 0)->orderBy('code', 'ASC')->findAll();
    }
}
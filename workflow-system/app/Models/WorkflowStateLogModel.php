<?php

namespace App\Models;

use CodeIgniter\Model;

class WorkflowStateLogModel extends Model
{
    protected $table            = 'workflow_state_log';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'project_id', 'collaboration_id', 'from_node_template_id', 'to_node_template_id',
        'trigger_condition', 'trigger_user_uid', 'state_data',
        'created_at', 'creator', 'updated_at', 'updater', 'deleted_at', 'deleter'
    ];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'project_id' => 'required|integer',
        'collaboration_id' => 'integer',
        'from_node_template_id' => 'integer',
        'to_node_template_id' => 'integer',
        'trigger_condition' => 'max_length[100]',
        'trigger_user_uid' => 'max_length[16]',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['setCreatedAt'];
    protected $beforeUpdate   = ['setUpdatedAt'];

    protected function setCreatedAt(array $data)
    {
        if (!isset($data['data']['created_at'])) {
            $data['data']['created_at'] = time();
        }
        return $data;
    }

    protected function setUpdatedAt(array $data)
    {
        $data['data']['updated_at'] = time();
        return $data;
    }

    /**
     * 获取项目的状态变更日志
     */
    public function getProjectStateLogs($projectId, $collaborationId = null)
    {
        $builder = $this->select('workflow_state_log.*,
                                from_node.node_name as from_node_name,
                                to_node.node_name as to_node_name,
                                user.name as trigger_user_name')
                        ->join('node_template as from_node', 'from_node.id = workflow_state_log.from_node_template_id', 'left')
                        ->join('node_template as to_node', 'to_node.id = workflow_state_log.to_node_template_id', 'left')
                        ->join('user', 'user.user_uid = workflow_state_log.trigger_user_uid', 'left')
                        ->where(['workflow_state_log.project_id' => $projectId, 'workflow_state_log.deleted_at' => 0]);

        if ($collaborationId !== null) {
            $builder->where('workflow_state_log.collaboration_id', $collaborationId);
        }

        return $builder->orderBy('workflow_state_log.created_at', 'ASC')
                      ->findAll();
    }
}


class ProjectActionLogModel extends Model
{
    protected $table            = 'project_action_log';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'project_id', 'collaboration_id', 'node_template_id', 'action_type',
        'action_by_uid', 'action_data', 'remark',
        'created_at', 'creator', 'updated_at', 'updater', 'deleted_at', 'deleter'
    ];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'project_id' => 'required|integer',
        'collaboration_id' => 'integer',
        'node_template_id' => 'integer',
        'action_type' => 'required|in_list[1,2,3,4,5,6,7,8]',
        'action_by_uid' => 'required|max_length[16]',
        'remark' => 'max_length[500]',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['setCreatedAt'];
    protected $beforeUpdate   = ['setUpdatedAt'];

    protected function setCreatedAt(array $data)
    {
        if (!isset($data['data']['created_at'])) {
            $data['data']['created_at'] = time();
        }
        return $data;
    }

    protected function setUpdatedAt(array $data)
    {
        $data['data']['updated_at'] = time();
        return $data;
    }

    /**
     * 获取项目的操作日志
     */
    public function getProjectActionLogs($projectId, $collaborationId = null, $limit = 50, $offset = 0)
    {
        $builder = $this->select('project_action_log.*,
                                node_template.node_name,
                                user.name as action_by_name')
                        ->join('node_template', 'node_template.id = project_action_log.node_template_id', 'left')
                        ->join('user', 'user.user_uid = project_action_log.action_by_uid', 'left')
                        ->where(['project_action_log.project_id' => $projectId, 'project_action_log.deleted_at' => 0]);

        if ($collaborationId !== null) {
            $builder->where('project_action_log.collaboration_id', $collaborationId);
        }

        return $builder->orderBy('project_action_log.created_at', 'DESC')
                      ->limit($limit, $offset)
                      ->findAll();
    }

    /**
     * 获取操作类型文本
     */
    public function getActionTypeText($actionType)
    {
        $types = [
            1 => '提交',
            2 => '审核通过',
            3 => '审核驳回',
            4 => '更新',
            5 => '选择',
            6 => '上传',
            7 => '交付',
            8 => '邮件'
        ];

        return $types[$actionType] ?? '未知操作';
    }
}
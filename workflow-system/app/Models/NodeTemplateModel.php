<?php

namespace App\Models;

use CodeIgniter\Model;

class NodeTemplateModel extends Model
{
    protected $table            = 'node_template';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'node_code', 'node_name', 'assignee_role_id', 'node_type', 'is_optional', 'action_config',
        'created_at', 'creator', 'updated_at', 'updater', 'deleted_at', 'deleter'
    ];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'node_code' => 'required|max_length[32]|is_unique[node_template.node_code]',
        'node_name' => 'required|max_length[50]',
        'assignee_role_id' => 'required|integer',
        'node_type' => 'required|in_list[1,2]',
    ];
    protected $validationMessages   = [
        'node_code' => [
            'required' => '节点编码必填',
            'max_length' => '节点编码最多32个字符',
            'is_unique' => '节点编码已存在'
        ],
        'node_name' => [
            'required' => '节点名称必填',
            'max_length' => '节点名称最多50个字符'
        ]
    ];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['setCreatedAt'];
    protected $beforeUpdate   = ['setUpdatedAt'];

    protected function setCreatedAt(array $data)
    {
        if (!isset($data['data']['created_at'])) {
            $data['data']['created_at'] = time();
        }
        return $data;
    }

    protected function setUpdatedAt(array $data)
    {
        $data['data']['updated_at'] = time();
        return $data;
    }

    /**
     * 根据节点编码获取模板
     */
    public function getByNodeCode($nodeCode)
    {
        return $this->where(['node_code' => $nodeCode, 'deleted_at' => 0])->first();
    }

    /**
     * 根据节点类型获取模板列表
     */
    public function getByNodeType($nodeType)
    {
        return $this->select('node_template.*, role.name as assignee_role_name')
                    ->join('role', 'role.id = node_template.assignee_role_id', 'left')
                    ->where(['node_template.node_type' => $nodeType, 'node_template.deleted_at' => 0])
                    ->orderBy('node_template.id', 'ASC')
                    ->findAll();
    }

    /**
     * 获取主流程节点模板
     */
    public function getMainFlowTemplates()
    {
        return $this->getByNodeType(1);
    }

    /**
     * 获取协同流程节点模板
     */
    public function getCollaborationFlowTemplates()
    {
        return $this->getByNodeType(2);
    }

    /**
     * 获取所有可用的节点模板（包含角色信息）
     */
    public function getAllWithRoles()
    {
        return $this->select('node_template.*, role.name as assignee_role_name')
                    ->join('role', 'role.id = node_template.assignee_role_id', 'left')
                    ->where('node_template.deleted_at', 0)
                    ->orderBy('node_template.node_type, node_template.id')
                    ->findAll();
    }
}
<?php

namespace App\Services;

use App\Models\ProjectModel;
use App\Models\ProjectCollaborationModel;
use App\Models\ProjectNodeInstanceModel;
use App\Models\ProjectActionLogModel;
use App\Models\WorkflowStateLogModel;
use App\Libraries\WorkflowEngine;
use CodeIgniter\Database\BaseConnection;

class ProjectService
{
    protected $projectModel;
    protected $collaborationModel;
    protected $nodeInstanceModel;
    protected $actionLogModel;
    protected $stateLogModel;
    protected $workflowEngine;
    protected $db;

    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->collaborationModel = new ProjectCollaborationModel();
        $this->nodeInstanceModel = new ProjectNodeInstanceModel();
        $this->actionLogModel = new ProjectActionLogModel();
        $this->stateLogModel = new WorkflowStateLogModel();
        $this->workflowEngine = new WorkflowEngine();
        $this->db = \Config\Database::connect();
    }

    /**
     * 创建新项目
     * 
     * @param array $projectData 项目数据
     * @param string $creatorUid 创建者UID
     * @return array
     */
    public function createProject(array $projectData, string $creatorUid): array
    {
        $this->db->transStart();

        try {
            // 生成项目编号
            $projectNo = $this->generateProjectNo($projectData['product_line_id']);
            
            // 准备项目数据
            $data = [
                'project_no' => $projectNo,
                'title' => $projectData['title'],
                'product_line_id' => $projectData['product_line_id'],
                'sales_owner_uid' => $projectData['sales_owner_uid'],
                'main_department_id' => $projectData['main_department_id'],
                'main_department_manager_uid' => $projectData['main_department_manager_uid'],
                'main_executor_uid' => $projectData['main_executor_uid'],
                'status' => 0, // 草稿状态
                'creator' => $creatorUid
            ];

            // 创建项目
            $projectId = $this->projectModel->insert($data);
            if (!$projectId) {
                throw new \Exception('创建项目失败');
            }

            // 使用工作流引擎初始化项目流程
            $workflowResult = $this->workflowEngine->createProject($data, $creatorUid);
            if (!$workflowResult['success']) {
                throw new \Exception($workflowResult['message']);
            }

            $this->db->transComplete();

            if ($this->db->transStatus() === false) {
                throw new \Exception('创建项目事务失败');
            }

            return [
                'success' => true,
                'data' => [
                    'project_id' => $projectId,
                    'project_no' => $projectNo
                ],
                'message' => '项目创建成功'
            ];

        } catch (\Exception $e) {
            $this->db->transRollback();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 更新项目信息
     * 
     * @param int $projectId 项目ID
     * @param array $updateData 更新数据
     * @param string $updaterUid 更新者UID
     * @return array
     */
    public function updateProject(int $projectId, array $updateData, string $updaterUid): array
    {
        try {
            // 检查项目是否存在
            $project = $this->projectModel->find($projectId);
            if (!$project) {
                return [
                    'success' => false,
                    'message' => '项目不存在'
                ];
            }

            // 准备更新数据
            $data = ['updater' => $updaterUid];
            $allowedFields = ['title', 'main_department_manager_uid', 'main_executor_uid'];
            
            foreach ($allowedFields as $field) {
                if (isset($updateData[$field])) {
                    $data[$field] = $updateData[$field];
                }
            }

            // 更新项目
            $result = $this->projectModel->update($projectId, $data);
            if (!$result) {
                return [
                    'success' => false,
                    'message' => '更新项目失败'
                ];
            }

            // 记录操作日志
            $this->logProjectAction($projectId, 0, 0, 4, $updaterUid, $updateData, '更新项目信息');

            return [
                'success' => true,
                'message' => '项目更新成功'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 更新项目状态
     * 
     * @param int $projectId 项目ID
     * @param int $status 新状态
     * @param string $updaterUid 更新者UID
     * @return array
     */
    public function updateProjectStatus(int $projectId, int $status, string $updaterUid): array
    {
        try {
            // 检查项目是否存在
            $project = $this->projectModel->find($projectId);
            if (!$project) {
                return [
                    'success' => false,
                    'message' => '项目不存在'
                ];
            }

            // 验证状态值
            $validStatuses = [0, 1, 2, 3]; // 草稿/进行中/已结束/已取消
            if (!in_array($status, $validStatuses)) {
                return [
                    'success' => false,
                    'message' => '无效的项目状态'
                ];
            }

            // 更新项目状态
            $result = $this->projectModel->update($projectId, [
                'status' => $status,
                'updater' => $updaterUid
            ]);

            if (!$result) {
                return [
                    'success' => false,
                    'message' => '更新项目状态失败'
                ];
            }

            // 记录操作日志
            $statusTexts = [0 => '草稿', 1 => '进行中', 2 => '已结束', 3 => '已取消'];
            $this->logProjectAction($projectId, 0, 0, 4, $updaterUid, 
                ['old_status' => $project['status'], 'new_status' => $status], 
                '更新项目状态为：' . $statusTexts[$status]);

            return [
                'success' => true,
                'message' => '项目状态更新成功'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 添加协同部门
     * 
     * @param int $projectId 项目ID
     * @param array $collaborationData 协同数据
     * @param string $operatorUid 操作者UID
     * @return array
     */
    public function addCollaboration(int $projectId, array $collaborationData, string $operatorUid): array
    {
        $this->db->transStart();

        try {
            // 检查项目是否存在
            $project = $this->projectModel->find($projectId);
            if (!$project) {
                throw new \Exception('项目不存在');
            }

            // 检查是否已存在相同部门的协同
            $existingCollaboration = $this->collaborationModel
                ->where(['project_id' => $projectId, 'department_id' => $collaborationData['department_id'], 'deleted_at' => 0])
                ->first();
            
            if ($existingCollaboration) {
                throw new \Exception('该部门已参与协同');
            }

            // 创建协同记录
            $data = [
                'project_id' => $projectId,
                'department_id' => $collaborationData['department_id'],
                'manager_uid' => $collaborationData['manager_uid'],
                'executor_uid' => $collaborationData['executor_uid'],
                'analyst_uid' => $collaborationData['analyst_uid'],
                'status' => 0, // 未开始
                'creator' => $operatorUid
            ];

            $collaborationId = $this->collaborationModel->insert($data);
            if (!$collaborationId) {
                throw new \Exception('创建协同记录失败');
            }

            // 使用工作流引擎初始化协同流程
            $workflowResult = $this->workflowEngine->initializeCollaborationWorkflow($projectId, $collaborationId, $project['product_line_id'], $operatorUid);
            if (!$workflowResult['success']) {
                throw new \Exception($workflowResult['message']);
            }

            $this->db->transComplete();

            if ($this->db->transStatus() === false) {
                throw new \Exception('添加协同部门事务失败');
            }

            // 记录操作日志
            $this->logProjectAction($projectId, $collaborationId, 0, 5, $operatorUid, $collaborationData, '添加协同部门');

            return [
                'success' => true,
                'data' => ['collaboration_id' => $collaborationId],
                'message' => '协同部门添加成功'
            ];

        } catch (\Exception $e) {
            $this->db->transRollback();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取项目详情（包含协同信息）
     * 
     * @param int $projectId 项目ID
     * @return array
     */
    public function getProjectDetail(int $projectId): array
    {
        try {
            // 获取项目基本信息
            $project = $this->projectModel->getProjectWithRelations($projectId);
            if (!$project) {
                return [
                    'success' => false,
                    'message' => '项目不存在'
                ];
            }

            // 获取协同部门信息
            $collaborations = $this->collaborationModel->getProjectCollaborations($projectId);

            // 获取项目当前节点实例
            $currentNodes = $this->nodeInstanceModel->getProjectCurrentNodes($projectId);

            return [
                'success' => true,
                'data' => [
                    'project' => $project,
                    'collaborations' => $collaborations,
                    'current_nodes' => $currentNodes
                ]
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取项目列表（带筛选和分页）
     * 
     * @param array $filters 筛选条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public function getProjectList(array $filters = [], int $page = 1, int $limit = 20): array
    {
        try {
            $projects = $this->projectModel->getProjectsWithPagination($filters, $page, $limit);
            return [
                'success' => true,
                'data' => $projects
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 软删除项目
     * 
     * @param int $projectId 项目ID
     * @param string $deleterUid 删除者UID
     * @return array
     */
    public function deleteProject(int $projectId, string $deleterUid): array
    {
        try {
            // 检查项目是否存在
            $project = $this->projectModel->find($projectId);
            if (!$project) {
                return [
                    'success' => false,
                    'message' => '项目不存在'
                ];
            }

            // 检查项目状态，只有草稿状态的项目可以删除
            if ($project['status'] != 0) {
                return [
                    'success' => false,
                    'message' => '只有草稿状态的项目可以删除'
                ];
            }

            // 软删除项目
            $result = $this->projectModel->update($projectId, [
                'deleted_at' => time(),
                'deleter' => $deleterUid
            ]);

            if (!$result) {
                return [
                    'success' => false,
                    'message' => '删除项目失败'
                ];
            }

            // 记录操作日志
            $this->logProjectAction($projectId, 0, 0, 8, $deleterUid, [], '删除项目');

            return [
                'success' => true,
                'message' => '项目删除成功'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 生成项目编号
     * 
     * @param int $productLineId 产品线ID
     * @return string
     */
    private function generateProjectNo(int $productLineId): string
    {
        $prefix = 'PRJ';
        $date = date('Ymd');
        $productLinePart = str_pad($productLineId, 2, '0', STR_PAD_LEFT);
        
        // 获取当天同产品线项目数量作为序号
        $count = $this->projectModel
            ->where('product_line_id', $productLineId)
            ->like('project_no', $prefix . $date . $productLinePart, 'after')
            ->countAllResults();
        
        $sequence = str_pad($count + 1, 3, '0', STR_PAD_LEFT);
        
        return $prefix . $date . $productLinePart . $sequence;
    }

    /**
     * 记录项目操作日志
     * 
     * @param int $projectId 项目ID
     * @param int $collaborationId 协同ID
     * @param int $nodeTemplateId 节点模板ID
     * @param int $actionType 操作类型
     * @param string $actionByUid 操作者UID
     * @param array $actionData 操作数据
     * @param string $remark 备注
     */
    private function logProjectAction(int $projectId, int $collaborationId, int $nodeTemplateId, int $actionType, string $actionByUid, array $actionData, string $remark): void
    {
        $this->actionLogModel->insert([
            'project_id' => $projectId,
            'collaboration_id' => $collaborationId,
            'node_template_id' => $nodeTemplateId,
            'action_type' => $actionType,
            'action_by_uid' => $actionByUid,
            'action_data' => json_encode($actionData),
            'remark' => $remark,
            'creator' => $actionByUid
        ]);
    }
}
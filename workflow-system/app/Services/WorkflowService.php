<?php

namespace App\Services;

use App\Models\ProjectModel;
use App\Models\ProjectNodeInstanceModel;
use App\Models\ProjectCollaborationModel;
use App\Models\WorkflowContextModel;
use App\Models\NodeTemplateModel;
use App\Models\NodeFlowRuleModel;
use App\Models\WorkflowDefinitionModel;
use App\Models\ProjectActionLogModel;
use App\Models\WorkflowStateLogModel;
use App\Libraries\WorkflowEngine;

class WorkflowService
{
    protected $projectModel;
    protected $nodeInstanceModel;
    protected $collaborationModel;
    protected $contextModel;
    protected $nodeTemplateModel;
    protected $flowRuleModel;
    protected $workflowDefinitionModel;
    protected $actionLogModel;
    protected $stateLogModel;
    protected $workflowEngine;

    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->nodeInstanceModel = new ProjectNodeInstanceModel();
        $this->collaborationModel = new ProjectCollaborationModel();
        $this->contextModel = new WorkflowContextModel();
        $this->nodeTemplateModel = new NodeTemplateModel();
        $this->flowRuleModel = new NodeFlowRuleModel();
        $this->workflowDefinitionModel = new WorkflowDefinitionModel();
        $this->actionLogModel = new ProjectActionLogModel();
        $this->stateLogModel = new WorkflowStateLogModel();
        $this->workflowEngine = new WorkflowEngine();
    }

    /**
     * 启动工作流
     * 
     * @param int $projectId 项目ID
     * @param string $operatorUid 操作者UID
     * @return array
     */
    public function startWorkflow(int $projectId, string $operatorUid): array
    {
        try {
            // 检查项目是否存在
            $project = $this->projectModel->find($projectId);
            if (!$project) {
                return [
                    'success' => false,
                    'message' => '项目不存在'
                ];
            }

            // 检查项目状态
            if ($project['status'] != 0) {
                return [
                    'success' => false,
                    'message' => '只有草稿状态的项目可以启动工作流'
                ];
            }

            // 使用工作流引擎启动流程
            $result = $this->workflowEngine->startMainWorkflow($projectId, $operatorUid);
            
            if ($result['success']) {
                // 更新项目状态为进行中
                $this->projectModel->update($projectId, [
                    'status' => 1,
                    'updater' => $operatorUid
                ]);

                // 记录操作日志
                $this->logAction($projectId, 0, 0, 1, $operatorUid, [], '启动项目工作流');
            }

            return $result;

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 执行节点流转
     * 
     * @param array $transitionData 流转数据
     * @param string $operatorUid 操作者UID
     * @return array
     */
    public function executeTransition(array $transitionData, string $operatorUid): array
    {
        try {
            $projectId = $transitionData['project_id'];
            $collaborationId = $transitionData['collaboration_id'] ?? 0;
            $fromNodeTemplateId = $transitionData['from_node_template_id'];
            $toNodeTemplateId = $transitionData['to_node_template_id'];
            $condition = $transitionData['condition'] ?? 'default';
            $actionData = $transitionData['action_data'] ?? [];

            // 验证节点实例是否存在且可操作
            $nodeInstance = $this->nodeInstanceModel
                ->where([
                    'project_id' => $projectId,
                    'collaboration_id' => $collaborationId,
                    'node_template_id' => $fromNodeTemplateId,
                    'status' => 1 // 进行中
                ])
                ->first();

            if (!$nodeInstance) {
                return [
                    'success' => false,
                    'message' => '节点实例不存在或不可操作'
                ];
            }

            // 使用工作流引擎执行流转
            $result = $this->workflowEngine->executeTransition(
                $projectId,
                $collaborationId,
                $fromNodeTemplateId,
                $toNodeTemplateId,
                $condition,
                $operatorUid,
                $actionData
            );

            if ($result['success']) {
                // 记录操作日志
                $this->logAction($projectId, $collaborationId, $fromNodeTemplateId, 1, $operatorUid, $actionData, '执行节点流转');
            }

            return $result;

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 审核通过
     * 
     * @param array $approveData 审核数据
     * @param string $operatorUid 操作者UID
     * @return array
     */
    public function approve(array $approveData, string $operatorUid): array
    {
        try {
            $projectId = $approveData['project_id'];
            $collaborationId = $approveData['collaboration_id'] ?? 0;
            $nodeTemplateId = $approveData['node_template_id'];
            $resultData = $approveData['result_data'] ?? [];
            $remark = $approveData['remark'] ?? '';

            // 验证节点实例
            $nodeInstance = $this->validateNodeInstance($projectId, $collaborationId, $nodeTemplateId);
            if (!$nodeInstance['success']) {
                return $nodeInstance;
            }

            // 更新节点实例状态为已完成
            $updateResult = $this->nodeInstanceModel->update($nodeInstance['data']['id'], [
                'status' => 2, // 已完成
                'finished_at' => time(),
                'result_data' => json_encode($resultData),
                'updater' => $operatorUid
            ]);

            if (!$updateResult) {
                return [
                    'success' => false,
                    'message' => '更新节点状态失败'
                ];
            }

            // 记录操作日志
            $this->logAction($projectId, $collaborationId, $nodeTemplateId, 2, $operatorUid, $resultData, $remark ?: '审核通过');

            // 尝试自动流转到下一个节点
            $this->tryAutoTransition($projectId, $collaborationId, $nodeTemplateId, $operatorUid);

            return [
                'success' => true,
                'message' => '审核通过成功'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 审核驳回
     * 
     * @param array $rejectData 驳回数据
     * @param string $operatorUid 操作者UID
     * @return array
     */
    public function reject(array $rejectData, string $operatorUid): array
    {
        try {
            $projectId = $rejectData['project_id'];
            $collaborationId = $rejectData['collaboration_id'] ?? 0;
            $nodeTemplateId = $rejectData['node_template_id'];
            $rejectToNodeId = $rejectData['reject_to_node_id'] ?? null;
            $reason = $rejectData['reason'] ?? '';

            // 验证节点实例
            $nodeInstance = $this->validateNodeInstance($projectId, $collaborationId, $nodeTemplateId);
            if (!$nodeInstance['success']) {
                return $nodeInstance;
            }

            // 更新当前节点状态为已驳回
            $this->nodeInstanceModel->update($nodeInstance['data']['id'], [
                'status' => 3, // 已驳回
                'finished_at' => time(),
                'result_data' => json_encode(['reject_reason' => $reason]),
                'updater' => $operatorUid
            ]);

            // 记录操作日志
            $this->logAction($projectId, $collaborationId, $nodeTemplateId, 3, $operatorUid, ['reason' => $reason], $reason ?: '审核驳回');

            // 执行驳回流转
            if ($rejectToNodeId) {
                $this->workflowEngine->executeTransition(
                    $projectId,
                    $collaborationId,
                    $nodeTemplateId,
                    $rejectToNodeId,
                    'reject',
                    $operatorUid,
                    ['reject_reason' => $reason]
                );
            }

            return [
                'success' => true,
                'message' => '审核驳回成功'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 提交节点
     * 
     * @param array $submitData 提交数据
     * @param string $operatorUid 操作者UID
     * @return array
     */
    public function submit(array $submitData, string $operatorUid): array
    {
        try {
            $projectId = $submitData['project_id'];
            $collaborationId = $submitData['collaboration_id'] ?? 0;
            $nodeTemplateId = $submitData['node_template_id'];
            $resultData = $submitData['result_data'] ?? [];

            // 验证节点实例
            $nodeInstance = $this->validateNodeInstance($projectId, $collaborationId, $nodeTemplateId);
            if (!$nodeInstance['success']) {
                return $nodeInstance;
            }

            // 更新节点实例
            $this->nodeInstanceModel->update($nodeInstance['data']['id'], [
                'status' => 2, // 已完成
                'finished_at' => time(),
                'result_data' => json_encode($resultData),
                'updater' => $operatorUid
            ]);

            // 记录操作日志
            $this->logAction($projectId, $collaborationId, $nodeTemplateId, 1, $operatorUid, $resultData, '提交节点');

            // 尝试自动流转
            $this->tryAutoTransition($projectId, $collaborationId, $nodeTemplateId, $operatorUid);

            return [
                'success' => true,
                'message' => '提交成功'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 跳过节点
     * 
     * @param array $skipData 跳过数据
     * @param string $operatorUid 操作者UID
     * @return array
     */
    public function skipNode(array $skipData, string $operatorUid): array
    {
        try {
            $projectId = $skipData['project_id'];
            $collaborationId = $skipData['collaboration_id'] ?? 0;
            $nodeTemplateId = $skipData['node_template_id'];
            $reason = $skipData['reason'] ?? '';

            // 检查节点是否可跳过
            $nodeTemplate = $this->nodeTemplateModel->find($nodeTemplateId);
            if (!$nodeTemplate || !$nodeTemplate['is_optional']) {
                return [
                    'success' => false,
                    'message' => '该节点不可跳过'
                ];
            }

            // 验证节点实例
            $nodeInstance = $this->validateNodeInstance($projectId, $collaborationId, $nodeTemplateId);
            if (!$nodeInstance['success']) {
                return $nodeInstance;
            }

            // 更新节点状态为已跳过
            $this->nodeInstanceModel->update($nodeInstance['data']['id'], [
                'status' => 4, // 已跳过
                'finished_at' => time(),
                'result_data' => json_encode(['skip_reason' => $reason]),
                'updater' => $operatorUid
            ]);

            // 记录操作日志
            $this->logAction($projectId, $collaborationId, $nodeTemplateId, 5, $operatorUid, ['reason' => $reason], $reason ?: '跳过节点');

            // 尝试自动流转
            $this->tryAutoTransition($projectId, $collaborationId, $nodeTemplateId, $operatorUid);

            return [
                'success' => true,
                'message' => '节点跳过成功'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取项目节点实例列表
     * 
     * @param int $projectId 项目ID
     * @param int $collaborationId 协同ID
     * @return array
     */
    public function getProjectNodes(int $projectId, int $collaborationId = null): array
    {
        try {
            $nodes = $this->nodeInstanceModel->getProjectNodeInstances($projectId, $collaborationId);
            return [
                'success' => true,
                'data' => $nodes
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取工作流上下文
     * 
     * @param int $projectId 项目ID
     * @param int $collaborationId 协同ID
     * @return array
     */
    public function getContext(int $projectId, int $collaborationId = 0): array
    {
        try {
            $context = $this->contextModel->getProjectContext($projectId, $collaborationId);
            return [
                'success' => true,
                'data' => $context
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 更新工作流上下文
     * 
     * @param int $projectId 项目ID
     * @param int $collaborationId 协同ID
     * @param array $contextData 上下文数据
     * @param string $operatorUid 操作者UID
     * @return array
     */
    public function updateContext(int $projectId, int $collaborationId, array $contextData, string $operatorUid): array
    {
        try {
            foreach ($contextData as $key => $value) {
                $dataType = is_array($value) ? 'json' : (is_int($value) ? 'int' : 'string');
                $contextValue = is_array($value) ? json_encode($value) : (string)$value;

                $this->contextModel->setContext($projectId, $collaborationId, $key, $contextValue, $dataType, $operatorUid);
            }

            return [
                'success' => true,
                'message' => '上下文更新成功'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取产品线流转规则
     * 
     * @param int $productLineId 产品线ID
     * @return array
     */
    public function getFlowRules(int $productLineId): array
    {
        try {
            $rules = $this->flowRuleModel->getProductLineRules($productLineId);
            return [
                'success' => true,
                'data' => $rules
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 验证节点实例
     * 
     * @param int $projectId 项目ID
     * @param int $collaborationId 协同ID
     * @param int $nodeTemplateId 节点模板ID
     * @return array
     */
    private function validateNodeInstance(int $projectId, int $collaborationId, int $nodeTemplateId): array
    {
        $nodeInstance = $this->nodeInstanceModel
            ->where([
                'project_id' => $projectId,
                'collaboration_id' => $collaborationId,
                'node_template_id' => $nodeTemplateId,
                'deleted_at' => 0
            ])
            ->first();

        if (!$nodeInstance) {
            return [
                'success' => false,
                'message' => '节点实例不存在'
            ];
        }

        if ($nodeInstance['status'] != 1) { // 不是进行中状态
            return [
                'success' => false,
                'message' => '节点状态不正确，无法操作'
            ];
        }

        return [
            'success' => true,
            'data' => $nodeInstance
        ];
    }

    /**
     * 尝试自动流转到下一个节点
     * 
     * @param int $projectId 项目ID
     * @param int $collaborationId 协同ID
     * @param int $currentNodeTemplateId 当前节点模板ID
     * @param string $operatorUid 操作者UID
     */
    private function tryAutoTransition(int $projectId, int $collaborationId, int $currentNodeTemplateId, string $operatorUid): void
    {
        try {
            // 获取项目信息以确定产品线
            $project = $this->projectModel->find($projectId);
            if (!$project) {
                return;
            }

            // 查找默认流转规则
            $defaultRule = $this->flowRuleModel
                ->where([
                    'product_line_id' => $project['product_line_id'],
                    'from_node_template_id' => $currentNodeTemplateId,
                    'condition_type' => 'default',
                    'is_active' => 1
                ])
                ->orderBy('priority', 'ASC')
                ->first();

            if ($defaultRule) {
                $this->workflowEngine->executeTransition(
                    $projectId,
                    $collaborationId,
                    $currentNodeTemplateId,
                    $defaultRule['to_node_template_id'],
                    'default',
                    $operatorUid,
                    []
                );
            }

        } catch (\Exception $e) {
            // 自动流转失败不影响主流程
            log_message('error', '自动流转失败: ' . $e->getMessage());
        }
    }

    /**
     * 记录操作日志
     * 
     * @param int $projectId 项目ID
     * @param int $collaborationId 协同ID
     * @param int $nodeTemplateId 节点模板ID
     * @param int $actionType 操作类型
     * @param string $actionByUid 操作者UID
     * @param array $actionData 操作数据
     * @param string $remark 备注
     */
    private function logAction(int $projectId, int $collaborationId, int $nodeTemplateId, int $actionType, string $actionByUid, array $actionData, string $remark): void
    {
        $this->actionLogModel->insert([
            'project_id' => $projectId,
            'collaboration_id' => $collaborationId,
            'node_template_id' => $nodeTemplateId,
            'action_type' => $actionType,
            'action_by_uid' => $actionByUid,
            'action_data' => json_encode($actionData),
            'remark' => $remark,
            'creator' => $actionByUid
        ]);
    }
}
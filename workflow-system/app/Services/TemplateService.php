<?php

namespace App\Services;

use App\Models\NodeTemplateModel;
use App\Models\WorkflowDefinitionModel;
use App\Models\NodeFlowRuleModel;

class TemplateService
{
    protected $nodeTemplateModel;
    protected $workflowDefinitionModel;
    protected $flowRuleModel;

    public function __construct()
    {
        $this->nodeTemplateModel = new NodeTemplateModel();
        $this->workflowDefinitionModel = new WorkflowDefinitionModel();
        $this->flowRuleModel = new NodeFlowRuleModel();
    }

    // ==================== 节点模板管理 ====================

    /**
     * 获取节点模板列表
     * 
     * @param array $filters 筛选条件
     * @return array
     */
    public function getNodeTemplates(array $filters = []): array
    {
        try {
            $templates = $this->nodeTemplateModel->getTemplatesWithFilters($filters);
            return [
                'success' => true,
                'data' => $templates
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 创建节点模板
     * 
     * @param array $templateData 模板数据
     * @param string $creatorUid 创建者UID
     * @return array
     */
    public function createNodeTemplate(array $templateData, string $creatorUid): array
    {
        try {
            // 验证必填字段
            $requiredFields = ['node_code', 'node_name', 'assignee_role_id', 'node_type'];
            foreach ($requiredFields as $field) {
                if (!isset($templateData[$field]) || empty($templateData[$field])) {
                    return [
                        'success' => false,
                        'message' => "字段 {$field} 不能为空"
                    ];
                }
            }

            // 检查节点编码是否已存在
            $existingTemplate = $this->nodeTemplateModel
                ->where('node_code', $templateData['node_code'])
                ->where('deleted_at', 0)
                ->first();

            if ($existingTemplate) {
                return [
                    'success' => false,
                    'message' => '节点编码已存在'
                ];
            }

            // 准备插入数据
            $data = [
                'node_code' => $templateData['node_code'],
                'node_name' => $templateData['node_name'],
                'assignee_role_id' => $templateData['assignee_role_id'],
                'node_type' => $templateData['node_type'],
                'is_optional' => $templateData['is_optional'] ?? 0,
                'action_config' => isset($templateData['action_config']) ? json_encode($templateData['action_config']) : null,
                'creator' => $creatorUid
            ];

            $templateId = $this->nodeTemplateModel->insert($data);

            if (!$templateId) {
                return [
                    'success' => false,
                    'message' => '创建节点模板失败'
                ];
            }

            return [
                'success' => true,
                'data' => ['template_id' => $templateId],
                'message' => '节点模板创建成功'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取节点模板详情
     * 
     * @param int $templateId 模板ID
     * @return array
     */
    public function getNodeTemplate(int $templateId): array
    {
        try {
            $template = $this->nodeTemplateModel->getTemplateWithRole($templateId);
            
            if (!$template) {
                return [
                    'success' => false,
                    'message' => '节点模板不存在'
                ];
            }

            return [
                'success' => true,
                'data' => $template
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 更新节点模板
     * 
     * @param int $templateId 模板ID
     * @param array $updateData 更新数据
     * @param string $updaterUid 更新者UID
     * @return array
     */
    public function updateNodeTemplate(int $templateId, array $updateData, string $updaterUid): array
    {
        try {
            // 检查模板是否存在
            $template = $this->nodeTemplateModel->find($templateId);
            if (!$template) {
                return [
                    'success' => false,
                    'message' => '节点模板不存在'
                ];
            }

            // 如果更新节点编码，检查重复
            if (isset($updateData['node_code']) && $updateData['node_code'] !== $template['node_code']) {
                $existingTemplate = $this->nodeTemplateModel
                    ->where('node_code', $updateData['node_code'])
                    ->where('deleted_at', 0)
                    ->where('id !=', $templateId)
                    ->first();

                if ($existingTemplate) {
                    return [
                        'success' => false,
                        'message' => '节点编码已存在'
                    ];
                }
            }

            // 准备更新数据
            $data = ['updater' => $updaterUid];
            $allowedFields = ['node_code', 'node_name', 'assignee_role_id', 'node_type', 'is_optional'];
            
            foreach ($allowedFields as $field) {
                if (isset($updateData[$field])) {
                    $data[$field] = $updateData[$field];
                }
            }

            if (isset($updateData['action_config'])) {
                $data['action_config'] = json_encode($updateData['action_config']);
            }

            $result = $this->nodeTemplateModel->update($templateId, $data);

            if (!$result) {
                return [
                    'success' => false,
                    'message' => '更新节点模板失败'
                ];
            }

            return [
                'success' => true,
                'message' => '节点模板更新成功'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 删除节点模板
     * 
     * @param int $templateId 模板ID
     * @param string $deleterUid 删除者UID
     * @return array
     */
    public function deleteNodeTemplate(int $templateId, string $deleterUid): array
    {
        try {
            // 检查模板是否存在
            $template = $this->nodeTemplateModel->find($templateId);
            if (!$template) {
                return [
                    'success' => false,
                    'message' => '节点模板不存在'
                ];
            }

            // 检查是否有工作流定义在使用此模板
            $usedInWorkflow = $this->workflowDefinitionModel
                ->where('node_template_id', $templateId)
                ->where('deleted_at', 0)
                ->first();

            if ($usedInWorkflow) {
                return [
                    'success' => false,
                    'message' => '该节点模板正在被工作流定义使用，无法删除'
                ];
            }

            // 软删除模板
            $result = $this->nodeTemplateModel->update($templateId, [
                'deleted_at' => time(),
                'deleter' => $deleterUid
            ]);

            if (!$result) {
                return [
                    'success' => false,
                    'message' => '删除节点模板失败'
                ];
            }

            return [
                'success' => true,
                'message' => '节点模板删除成功'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    // ==================== 工作流定义管理 ====================

    /**
     * 获取工作流定义列表
     * 
     * @param array $filters 筛选条件
     * @return array
     */
    public function getWorkflowDefinitions(array $filters = []): array
    {
        try {
            $definitions = $this->workflowDefinitionModel->getDefinitionsWithRelations($filters);
            return [
                'success' => true,
                'data' => $definitions
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 创建工作流定义
     * 
     * @param array $definitionData 定义数据
     * @param string $creatorUid 创建者UID
     * @return array
     */
    public function createWorkflowDefinition(array $definitionData, string $creatorUid): array
    {
        try {
            // 验证必填字段
            $requiredFields = ['product_line_id', 'node_template_id', 'flow_type', 'order_no'];
            foreach ($requiredFields as $field) {
                if (!isset($definitionData[$field])) {
                    return [
                        'success' => false,
                        'message' => "字段 {$field} 不能为空"
                    ];
                }
            }

            // 检查是否已存在相同的定义
            $existingDefinition = $this->workflowDefinitionModel
                ->where([
                    'product_line_id' => $definitionData['product_line_id'],
                    'node_template_id' => $definitionData['node_template_id'],
                    'flow_type' => $definitionData['flow_type'],
                    'deleted_at' => 0
                ])
                ->first();

            if ($existingDefinition) {
                return [
                    'success' => false,
                    'message' => '该产品线下已存在相同的工作流定义'
                ];
            }

            $data = [
                'product_line_id' => $definitionData['product_line_id'],
                'node_template_id' => $definitionData['node_template_id'],
                'flow_type' => $definitionData['flow_type'],
                'order_no' => $definitionData['order_no'],
                'is_start_node' => $definitionData['is_start_node'] ?? 0,
                'is_end_node' => $definitionData['is_end_node'] ?? 0,
                'creator' => $creatorUid
            ];

            $definitionId = $this->workflowDefinitionModel->insert($data);

            if (!$definitionId) {
                return [
                    'success' => false,
                    'message' => '创建工作流定义失败'
                ];
            }

            return [
                'success' => true,
                'data' => ['definition_id' => $definitionId],
                'message' => '工作流定义创建成功'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取产品线工作流定义
     * 
     * @param int $productLineId 产品线ID
     * @return array
     */
    public function getProductLineWorkflows(int $productLineId): array
    {
        try {
            $workflows = $this->workflowDefinitionModel->getProductLineWorkflows($productLineId);
            
            // 按流程类型分组
            $groupedWorkflows = [
                'main_workflow' => [],
                'collaboration_workflow' => []
            ];

            foreach ($workflows as $workflow) {
                if ($workflow['flow_type'] == 1) {
                    $groupedWorkflows['main_workflow'][] = $workflow;
                } else {
                    $groupedWorkflows['collaboration_workflow'][] = $workflow;
                }
            }

            return [
                'success' => true,
                'data' => $groupedWorkflows
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    // ==================== 流转规则管理 ====================

    /**
     * 获取流转规则列表
     * 
     * @param array $filters 筛选条件
     * @return array
     */
    public function getFlowRules(array $filters = []): array
    {
        try {
            $rules = $this->flowRuleModel->getRulesWithTemplates($filters);
            return [
                'success' => true,
                'data' => $rules
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 创建流转规则
     * 
     * @param array $ruleData 规则数据
     * @param string $creatorUid 创建者UID
     * @return array
     */
    public function createFlowRule(array $ruleData, string $creatorUid): array
    {
        try {
            // 验证必填字段
            $requiredFields = ['product_line_id', 'from_node_template_id', 'to_node_template_id', 'condition_type', 'condition_value'];
            foreach ($requiredFields as $field) {
                if (!isset($ruleData[$field]) || $ruleData[$field] === '') {
                    return [
                        'success' => false,
                        'message' => "字段 {$field} 不能为空"
                    ];
                }
            }

            $data = [
                'product_line_id' => $ruleData['product_line_id'],
                'from_node_template_id' => $ruleData['from_node_template_id'],
                'to_node_template_id' => $ruleData['to_node_template_id'],
                'condition_type' => $ruleData['condition_type'],
                'condition_value' => $ruleData['condition_value'],
                'condition_expression' => $ruleData['condition_expression'] ?? '',
                'is_active' => $ruleData['is_active'] ?? 1,
                'priority' => $ruleData['priority'] ?? 0,
                'creator' => $creatorUid
            ];

            $ruleId = $this->flowRuleModel->insert($data);

            if (!$ruleId) {
                return [
                    'success' => false,
                    'message' => '创建流转规则失败'
                ];
            }

            return [
                'success' => true,
                'data' => ['rule_id' => $ruleId],
                'message' => '流转规则创建成功'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取流转规则详情
     * 
     * @param int $ruleId 规则ID
     * @return array
     */
    public function getFlowRule(int $ruleId): array
    {
        try {
            $rule = $this->flowRuleModel->getRuleWithTemplates($ruleId);
            
            if (!$rule) {
                return [
                    'success' => false,
                    'message' => '流转规则不存在'
                ];
            }

            return [
                'success' => true,
                'data' => $rule
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 更新流转规则
     * 
     * @param int $ruleId 规则ID
     * @param array $updateData 更新数据
     * @param string $updaterUid 更新者UID
     * @return array
     */
    public function updateFlowRule(int $ruleId, array $updateData, string $updaterUid): array
    {
        try {
            // 检查规则是否存在
            $rule = $this->flowRuleModel->find($ruleId);
            if (!$rule) {
                return [
                    'success' => false,
                    'message' => '流转规则不存在'
                ];
            }

            // 准备更新数据
            $data = ['updater' => $updaterUid];
            $allowedFields = ['condition_type', 'condition_value', 'condition_expression', 'is_active', 'priority'];
            
            foreach ($allowedFields as $field) {
                if (isset($updateData[$field])) {
                    $data[$field] = $updateData[$field];
                }
            }

            $result = $this->flowRuleModel->update($ruleId, $data);

            if (!$result) {
                return [
                    'success' => false,
                    'message' => '更新流转规则失败'
                ];
            }

            return [
                'success' => true,
                'message' => '流转规则更新成功'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 删除流转规则
     * 
     * @param int $ruleId 规则ID
     * @param string $deleterUid 删除者UID
     * @return array
     */
    public function deleteFlowRule(int $ruleId, string $deleterUid): array
    {
        try {
            // 检查规则是否存在
            $rule = $this->flowRuleModel->find($ruleId);
            if (!$rule) {
                return [
                    'success' => false,
                    'message' => '流转规则不存在'
                ];
            }

            // 软删除规则
            $result = $this->flowRuleModel->update($ruleId, [
                'deleted_at' => time(),
                'deleter' => $deleterUid
            ]);

            if (!$result) {
                return [
                    'success' => false,
                    'message' => '删除流转规则失败'
                ];
            }

            return [
                'success' => true,
                'message' => '流转规则删除成功'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
}
<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class InitialDataSeeder extends Seeder
{
    public function run()
    {
        // 插入产品线数据
        $productLineData = [
            [
                'code' => 'A',
                'name' => '产品线A',
                'created_at' => time(),
                'creator' => 'system'
            ],
            [
                'code' => 'B',
                'name' => '产品线B',
                'created_at' => time(),
                'creator' => 'system'
            ],
            [
                'code' => 'C',
                'name' => '产品线C',
                'created_at' => time(),
                'creator' => 'system'
            ],
            [
                'code' => 'D',
                'name' => '产品线D',
                'created_at' => time(),
                'creator' => 'system'
            ]
        ];

        $this->db->table('product_line')->insertBatch($productLineData);

        // 插入角色数据
        $roleData = [
            [
                'id' => 1,
                'name' => '销售',
                'created_at' => time(),
                'creator' => 'system'
            ],
            [
                'id' => 2,
                'name' => '主部门负责人',
                'created_at' => time(),
                'creator' => 'system'
            ],
            [
                'id' => 3,
                'name' => '主部门执行人员',
                'created_at' => time(),
                'creator' => 'system'
            ],
            [
                'id' => 4,
                'name' => '协同部门负责人',
                'created_at' => time(),
                'creator' => 'system'
            ],
            [
                'id' => 5,
                'name' => '协同部门执行人员',
                'created_at' => time(),
                'creator' => 'system'
            ],
            [
                'id' => 6,
                'name' => '数据分析师',
                'created_at' => time(),
                'creator' => 'system'
            ],
            [
                'id' => 7,
                'name' => '商务负责人',
                'created_at' => time(),
                'creator' => 'system'
            ]
        ];

        $this->db->table('role')->insertBatch($roleData);

        // 插入部门数据
        $departmentData = [
            [
                'name' => '技术部',
                'is_main_dept' => 1,
                'is_collab_dept' => 1,
                'created_at' => time(),
                'creator' => 'system'
            ],
            [
                'name' => '运营部',
                'is_main_dept' => 1,
                'is_collab_dept' => 1,
                'created_at' => time(),
                'creator' => 'system'
            ],
            [
                'name' => '市场部',
                'is_main_dept' => 1,
                'is_collab_dept' => 1,
                'created_at' => time(),
                'creator' => 'system'
            ],
            [
                'name' => '数据部',
                'is_main_dept' => 0,
                'is_collab_dept' => 1,
                'created_at' => time(),
                'creator' => 'system'
            ],
            [
                'name' => '商务部',
                'is_main_dept' => 0,
                'is_collab_dept' => 1,
                'created_at' => time(),
                'creator' => 'system'
            ]
        ];

        $this->db->table('department')->insertBatch($departmentData);

        // 插入一些示例节点模板
        $nodeTemplateData = [
            [
                'node_code' => 'main_1',
                'node_name' => '项目发起',
                'assignee_role_id' => 1, // 销售
                'node_type' => 1, // 主流程
                'is_optional' => 0,
                'action_config' => json_encode([
                    'required_fields' => ['project_title', 'project_description'],
                    'auto_transition' => false
                ]),
                'created_at' => time(),
                'creator' => 'system'
            ],
            [
                'node_code' => 'main_2',
                'node_name' => '需求评审',
                'assignee_role_id' => 2, // 主部门负责人
                'node_type' => 1, // 主流程
                'is_optional' => 0,
                'action_config' => json_encode([
                    'required_fields' => ['review_result', 'review_comments'],
                    'auto_transition' => true
                ]),
                'created_at' => time(),
                'creator' => 'system'
            ],
            [
                'node_code' => 'collab_1',
                'node_name' => '技术评估',
                'assignee_role_id' => 4, // 协同部门负责人
                'node_type' => 2, // 协同流程
                'is_optional' => 1,
                'action_config' => json_encode([
                    'required_fields' => ['tech_assessment'],
                    'auto_transition' => false
                ]),
                'created_at' => time(),
                'creator' => 'system'
            ],
            [
                'node_code' => 'main_3',
                'node_name' => '项目执行',
                'assignee_role_id' => 3, // 主部门执行人员
                'node_type' => 1, // 主流程
                'is_optional' => 0,
                'action_config' => json_encode([
                    'required_fields' => ['execution_plan'],
                    'auto_transition' => false
                ]),
                'created_at' => time(),
                'creator' => 'system'
            ],
            [
                'node_code' => 'main_4',
                'node_name' => '项目验收',
                'assignee_role_id' => 2, // 主部门负责人
                'node_type' => 1, // 主流程
                'is_optional' => 0,
                'action_config' => json_encode([
                    'required_fields' => ['acceptance_result'],
                    'auto_transition' => true
                ]),
                'created_at' => time(),
                'creator' => 'system'
            ]
        ];

        $this->db->table('node_template')->insertBatch($nodeTemplateData);

        echo "基础数据初始化完成！\n";
        echo "- 产品线: " . count($productLineData) . " 条\n";
        echo "- 角色: " . count($roleData) . " 条\n";
        echo "- 部门: " . count($departmentData) . " 条\n";
        echo "- 节点模板: " . count($nodeTemplateData) . " 条\n";
    }
}
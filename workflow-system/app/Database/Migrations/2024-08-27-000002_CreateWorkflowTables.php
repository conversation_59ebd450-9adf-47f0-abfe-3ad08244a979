<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateWorkflowTables extends Migration
{
    public function up()
    {
        // 通用节点模板表（可复用）
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'node_code' => [
                'type' => 'VARCHAR',
                'constraint' => 32,
                'null' => false,
                'comment' => '节点编码：main_1, main_2, collab_1等',
            ],
            'node_name' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => false,
                'comment' => '节点名称',
            ],
            'assignee_role_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '节点处理角色ID',
            ],
            'node_type' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'unsigned' => true,
                'null' => false,
                'default' => 1,
                'comment' => '节点类型：1主流程/2协同流程',
            ],
            'is_optional' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '是否可跳过',
            ],
            'action_config' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => '节点操作配置（上传文件、选择人员等）',
            ],
            'created_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'creator' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'updated_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'updater' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'deleted_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'deleter' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey('node_code');
        $this->forge->addKey('node_type');
        $this->forge->createTable('node_template');

        // 产品线工作流定义表（产品线使用哪些节点）
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'product_line_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '产品线ID',
            ],
            'node_template_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '节点模板ID',
            ],
            'flow_type' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'unsigned' => true,
                'null' => false,
                'default' => 1,
                'comment' => '流程类型：1主流程/2协同流程',
            ],
            'order_no' => [
                'type' => 'TINYINT',
                'constraint' => 3,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '在该流程中的顺序',
            ],
            'is_start_node' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '是否起始节点',
            ],
            'is_end_node' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '是否结束节点',
            ],
            'created_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'creator' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'updated_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'updater' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'deleted_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'deleter' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey(['product_line_id', 'node_template_id', 'flow_type']);
        $this->forge->addKey('product_line_id');
        $this->forge->addKey('node_template_id');
        $this->forge->createTable('workflow_definition');

        // 节点流转规则表（定义节点间的流转条件）
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'product_line_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '产品线ID',
            ],
            'from_node_template_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '来源节点模板ID',
            ],
            'to_node_template_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '目标节点模板ID',
            ],
            'condition_type' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => false,
                'comment' => '流转条件类型：default/sign_status/approve_result/reject等',
            ],
            'condition_value' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
                'comment' => '流转条件值',
            ],
            'condition_expression' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => false,
                'comment' => '条件表达式（复杂条件）',
            ],
            'is_active' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'unsigned' => true,
                'null' => false,
                'default' => 1,
                'comment' => '是否启用',
            ],
            'priority' => [
                'type' => 'TINYINT',
                'constraint' => 3,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '优先级（数字越小优先级越高）',
            ],
            'created_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'creator' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'updated_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'updater' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'deleted_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'deleter' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addKey('product_line_id');
        $this->forge->addKey('from_node_template_id');
        $this->forge->addKey('to_node_template_id');
        $this->forge->addKey('condition_type');
        $this->forge->createTable('node_flow_rule');
    }

    public function down()
    {
        $this->forge->dropTable('node_flow_rule');
        $this->forge->dropTable('workflow_definition');
        $this->forge->dropTable('node_template');
    }
}
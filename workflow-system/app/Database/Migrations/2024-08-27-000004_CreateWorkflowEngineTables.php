<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateWorkflowEngineTables extends Migration
{
    public function up()
    {
        // 工作流执行上下文表（存储流程执行过程中的变量）
        $this->forge->addField([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'project_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '项目ID',
            ],
            'collaboration_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '协同ID：主流程为0',
            ],
            'context_key' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => false,
                'comment' => '上下文键',
            ],
            'context_value' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => '上下文值',
            ],
            'data_type' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => false,
                'default' => 'string',
                'comment' => '数据类型：string/int/json',
            ],
            'created_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'creator' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'updated_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'updater' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'deleted_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'deleter' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey(['project_id', 'collaboration_id', 'context_key']);
        $this->forge->addKey('project_id');
        $this->forge->createTable('workflow_context');

        // 工作流状态变更日志表
        $this->forge->addField([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'project_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '项目ID',
            ],
            'collaboration_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '协同ID：主流程为0',
            ],
            'from_node_template_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '来源节点模板ID',
            ],
            'to_node_template_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '目标节点模板ID',
            ],
            'trigger_condition' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
                'comment' => '触发条件',
            ],
            'trigger_user_uid' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'comment' => '触发用户UID',
            ],
            'state_data' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => '状态数据快照',
            ],
            'created_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'creator' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'updated_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'updater' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'deleted_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'deleter' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addKey('project_id');
        $this->forge->addKey('from_node_template_id');
        $this->forge->addKey('to_node_template_id');
        $this->forge->createTable('workflow_state_log');
    }

    public function down()
    {
        $this->forge->dropTable('workflow_state_log');
        $this->forge->dropTable('workflow_context');
    }
}
<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateBasicTables extends Migration
{
    public function up()
    {
        // 产品线表
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'code' => [
                'type' => 'CHAR',
                'constraint' => 1,
                'null' => false,
                'comment' => '产品线编码：A/B/C/D',
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 30,
                'null' => false,
                'comment' => '产品线名称',
            ],
            'created_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '创建时间',
            ],
            'creator' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
                'comment' => '创建人ID',
            ],
            'updated_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '更新时间',
            ],
            'updater' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
                'comment' => '更新人ID',
            ],
            'deleted_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '删除时间',
            ],
            'deleter' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
                'comment' => '删除人ID',
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey('code');
        $this->forge->createTable('product_line');

        // 角色表
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 30,
                'null' => false,
                'comment' => '角色名称',
            ],
            'created_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'creator' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'updated_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'updater' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'deleted_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'deleter' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->createTable('role');

        // 部门表
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => false,
                'comment' => '部门名称',
            ],
            'is_main_dept' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '是否可作为主部门：0否，1是',
            ],
            'is_collab_dept' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'unsigned' => true,
                'null' => false,
                'default' => 1,
                'comment' => '是否可作为协同部门：0否，1是',
            ],
            'created_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'creator' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'updated_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'updater' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'deleted_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'deleter' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->createTable('department');

        // 用户表
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_uid' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'comment' => '用户唯一ID',
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => false,
                'comment' => '姓名',
            ],
            'email' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
                'comment' => '邮箱',
            ],
            'department_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '所属部门ID',
            ],
            'role_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '主角色ID',
            ],
            'created_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'creator' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'updated_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'updater' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'deleted_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'deleter' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey('user_uid');
        $this->forge->addKey('department_id');
        $this->forge->addKey('role_id');
        $this->forge->createTable('user');
    }

    public function down()
    {
        $this->forge->dropTable('user');
        $this->forge->dropTable('department');
        $this->forge->dropTable('role');
        $this->forge->dropTable('product_line');
    }
}
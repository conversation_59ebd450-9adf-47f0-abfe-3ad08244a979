<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateProjectTables extends Migration
{
    public function up()
    {
        // 项目主表
        $this->forge->addField([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'project_no' => [
                'type' => 'VARCHAR',
                'constraint' => 40,
                'null' => false,
                'comment' => '项目编号',
            ],
            'title' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
                'comment' => '项目标题',
            ],
            'product_line_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '产品线ID',
            ],
            'sales_owner_uid' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'comment' => '销售发起人UID',
            ],
            'status' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '项目状态：0草稿/1进行中/2已结束/3已取消',
            ],
            'sign_status' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '签约状态：0未知/1未中标/2中标',
            ],
            'main_department_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '主部门ID',
            ],
            'main_department_manager_uid' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'comment' => '主部门负责人UID',
            ],
            'main_executor_uid' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'comment' => '主部门执行人员UID',
            ],
            'current_node_template_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '当前节点模板ID',
            ],
            'created_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'creator' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'updated_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'updater' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'deleted_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'deleter' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey('project_no');
        $this->forge->addKey('product_line_id');
        $this->forge->addKey('current_node_template_id');
        $this->forge->createTable('project');

        // 协同部门表
        $this->forge->addField([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'project_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '项目ID',
            ],
            'department_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '协同部门ID',
            ],
            'manager_uid' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'comment' => '协同部门负责人UID',
            ],
            'executor_uid' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'comment' => '协同执行人员UID',
            ],
            'analyst_uid' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'comment' => '数据分析师UID',
            ],
            'status' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '协同状态：0未开始/1进行中/2已完成/3已驳回',
            ],
            'current_node_template_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '当前协同节点模板ID',
            ],
            'created_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'creator' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'updated_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'updater' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'deleted_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'deleter' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addKey('project_id');
        $this->forge->addKey('department_id');
        $this->forge->createTable('project_collaboration');

        // 项目节点实例表
        $this->forge->addField([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'project_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '项目ID',
            ],
            'collaboration_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '协同ID：主流程为0',
            ],
            'node_template_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '节点模板ID',
            ],
            'assignee_uid' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'comment' => '当前处理人UID',
            ],
            'status' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '节点状态：0未开始/1进行中/2已完成/3已驳回/4已跳过',
            ],
            'started_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '开始时间',
            ],
            'finished_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '完成时间',
            ],
            'result_data' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => '节点处理结果数据',
            ],
            'version' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 1,
                'comment' => '版本号（驳回重置时递增）',
            ],
            'created_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'creator' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'updated_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'updater' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'deleted_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'deleter' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addKey('project_id');
        $this->forge->addKey('collaboration_id');
        $this->forge->addKey('node_template_id');
        $this->forge->addKey('status');
        $this->forge->createTable('project_node_instance');

        // 项目操作记录表
        $this->forge->addField([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'project_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '项目ID',
            ],
            'collaboration_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '协同ID：主流程为0',
            ],
            'node_template_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '节点模板ID',
            ],
            'action_type' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
                'comment' => '操作类型：1提交/2审核通过/3审核驳回/4更新/5选择/6上传/7交付/8邮件',
            ],
            'action_by_uid' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'comment' => '操作人UID',
            ],
            'action_data' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => '操作数据',
            ],
            'remark' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => false,
                'comment' => '备注',
            ],
            'created_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'creator' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'updated_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'updater' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
            'deleted_at' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'default' => 0,
            ],
            'deleter' => [
                'type' => 'CHAR',
                'constraint' => 16,
                'null' => false,
                'default' => '',
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addKey('project_id');
        $this->forge->addKey('node_template_id');
        $this->forge->addKey('action_type');
        $this->forge->createTable('project_action_log');
    }

    public function down()
    {
        $this->forge->dropTable('project_action_log');
        $this->forge->dropTable('project_node_instance');
        $this->forge->dropTable('project_collaboration');
        $this->forge->dropTable('project');
    }
}
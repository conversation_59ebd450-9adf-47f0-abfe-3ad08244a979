<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');
$routes->get('/templates', 'TemplateController::index');

// API 路由组
$routes->group('api/v1', ['namespace' => 'App\Controllers'], function($routes) {
    
    // 项目管理相关 API
    $routes->group('projects', function($routes) {
        $routes->get('', 'ProjectController::index');                    // 获取项目列表
        $routes->post('', 'ProjectController::create');                  // 创建新项目
        $routes->get('(:num)', 'ProjectController::show/$1');            // 获取项目详情
        $routes->put('(:num)', 'ProjectController::update/$1');          // 更新项目信息
        $routes->delete('(:num)', 'ProjectController::delete/$1');       // 删除项目
        $routes->put('(:num)/status', 'ProjectController::updateStatus/$1'); // 更新项目状态
        
        // 项目协同相关
        $routes->get('(:num)/collaborations', 'ProjectController::getCollaborations/$1'); // 获取协同部门
        $routes->post('(:num)/collaborations', 'ProjectController::addCollaboration/$1');  // 添加协同部门
        $routes->put('(:num)/collaborations/(:num)', 'ProjectController::updateCollaboration/$1/$2'); // 更新协同
        $routes->delete('(:num)/collaborations/(:num)', 'ProjectController::removeCollaboration/$1/$2'); // 移除协同
        
        // 项目日志相关
        $routes->get('(:num)/logs', 'ProjectController::getLogs/$1');     // 获取项目日志
        $routes->get('(:num)/state-logs', 'ProjectController::getStateLogs/$1'); // 获取状态变更日志
    });
    
    // 工作流程相关 API
    $routes->group('workflows', function($routes) {
        $routes->post('start', 'WorkflowController::startWorkflow');      // 启动工作流
        $routes->post('transition', 'WorkflowController::executeTransition'); // 执行流程流转
        $routes->post('approve', 'WorkflowController::approve');          // 审核通过
        $routes->post('reject', 'WorkflowController::reject');            // 审核驳回
        $routes->post('submit', 'WorkflowController::submit');            // 提交节点
        $routes->post('skip', 'WorkflowController::skipNode');            // 跳过节点
        
        // 节点实例相关
        $routes->get('projects/(:num)/nodes', 'WorkflowController::getProjectNodes/$1'); // 获取项目节点实例
        $routes->get('nodes/(:num)', 'WorkflowController::getNodeInstance/$1');          // 获取节点实例详情
        $routes->put('nodes/(:num)', 'WorkflowController::updateNodeInstance/$1');       // 更新节点实例
        
        // 工作流上下文
        $routes->get('projects/(:num)/context', 'WorkflowController::getContext/$1');    // 获取项目上下文
        $routes->put('projects/(:num)/context', 'WorkflowController::updateContext/$1'); // 更新项目上下文
        
        // 流程规则相关
        $routes->get('rules/product-line/(:num)', 'WorkflowController::getFlowRules/$1'); // 获取产品线流程规则
    });
    
    // 模板管理相关 API
    $routes->group('templates', function($routes) {
        // 节点模板
        $routes->get('nodes', 'TemplateController::getNodeTemplates');               // 获取节点模板列表
        $routes->post('nodes', 'TemplateController::createNodeTemplate');            // 创建节点模板
        $routes->get('nodes/(:num)', 'TemplateController::getNodeTemplate/$1');      // 获取节点模板详情
        $routes->put('nodes/(:num)', 'TemplateController::updateNodeTemplate/$1');   // 更新节点模板
        $routes->delete('nodes/(:num)', 'TemplateController::delete/$1'); // 删除节点模板
        
        // 工作流定义
        $routes->get('workflows', 'TemplateController::getWorkflowDefinitions');     // 获取工作流定义列表
        $routes->post('workflows', 'TemplateController::createWorkflowDefinition');  // 创建工作流定义
        $routes->get('workflows/(:num)', 'TemplateController::getWorkflowDefinition/$1'); // 获取工作流定义详情
        $routes->put('workflows/(:num)', 'TemplateController::updateWorkflowDefinition/$1'); // 更新工作流定义
        $routes->delete('workflows/(:num)', 'TemplateController::deleteWorkflowDefinition/$1'); // 删除工作流定义
        
        // 产品线工作流定义
        $routes->get('product-lines/(:num)/workflows', 'TemplateController::getProductLineWorkflows/$1'); // 获取产品线工作流
        
        // 流转规则
        $routes->get('flow-rules', 'TemplateController::getAllFlowRules');            // 获取流转规则列表
        $routes->post('flow-rules', 'TemplateController::createFlowRule');           // 创建流转规则
        $routes->put('flow-rules/(:num)', 'TemplateController::updateFlowRule/$1');  // 更新流转规则
        $routes->delete('flow-rules/(:num)', 'TemplateController::deleteFlowRule/$1'); // 删除流转规则
    });
    
    // 基础数据相关 API
    $routes->group('master', function($routes) {
        // 产品线
        $routes->get('product-lines', 'MasterController::getProductLines');          // 获取产品线列表
        $routes->post('product-lines', 'MasterController::createProductLine');       // 创建产品线
        $routes->get('product-lines/(:num)', 'MasterController::getProductLine/$1'); // 获取产品线详情
        $routes->put('product-lines/(:num)', 'MasterController::updateProductLine/$1'); // 更新产品线
        $routes->delete('product-lines/(:num)', 'MasterController::deleteProductLine/$1'); // 删除产品线
        
        // 部门
        $routes->get('departments', 'MasterController::getDepartments');             // 获取部门列表
        $routes->post('departments', 'MasterController::createDepartment');          // 创建部门
        $routes->get('departments/(:num)', 'MasterController::getDepartment/$1');    // 获取部门详情
        $routes->put('departments/(:num)', 'MasterController::updateDepartment/$1'); // 更新部门
        $routes->delete('departments/(:num)', 'MasterController::deleteDepartment/$1'); // 删除部门
        
        // 角色
        $routes->get('roles', 'MasterController::getRoles');                         // 获取角色列表
        $routes->post('roles', 'MasterController::createRole');                      // 创建角色
        $routes->get('roles/(:num)', 'MasterController::getRole/$1');               // 获取角色详情
        $routes->put('roles/(:num)', 'MasterController::updateRole/$1');            // 更新角色
        $routes->delete('roles/(:num)', 'MasterController::deleteRole/$1');         // 删除角色
        
        // 用户
        $routes->get('users', 'MasterController::getUsers');                        // 获取用户列表
        $routes->post('users', 'MasterController::createUser');                     // 创建用户
        $routes->get('users/(:segment)', 'MasterController::getUser/$1');           // 获取用户详情
        $routes->put('users/(:segment)', 'MasterController::updateUser/$1');        // 更新用户
        $routes->delete('users/(:segment)', 'MasterController::deleteUser/$1');     // 删除用户
        
        // 根据部门获取用户
        $routes->get('departments/(:num)/users', 'MasterController::getDepartmentUsers/$1'); // 获取部门用户
        // 根据角色获取用户
        $routes->get('roles/(:num)/users', 'MasterController::getRoleUsers/$1');     // 获取角色用户
    });
    
    // 仪表板和统计相关 API
    $routes->group('dashboard', function($routes) {
        $routes->get('summary', 'DashboardController::getSummary');                 // 获取汇总数据
        $routes->get('projects/statistics', 'DashboardController::getProjectStatistics'); // 获取项目统计
        $routes->get('workflows/statistics', 'DashboardController::getWorkflowStatistics'); // 获取工作流统计
        $routes->get('departments/workload', 'DashboardController::getDepartmentWorkload'); // 获取部门工作量
        $routes->get('pending-tasks', 'DashboardController::getPendingTasks');      // 获取待处理任务
    });
    
    // 导出相关 API
    $routes->group('export', function($routes) {
        $routes->get('projects', 'ExportController::exportProjects');               // 导出项目数据
        $routes->get('projects/(:num)/logs', 'ExportController::exportProjectLogs/$1'); // 导出项目日志
        $routes->get('workflows/statistics', 'ExportController::exportWorkflowStatistics'); // 导出工作流统计
    });
});

// 为了向后兼容，保留一些简化路由
$routes->resource('projects', ['controller' => 'ProjectController']);
$routes->resource('workflows', ['controller' => 'WorkflowController']);
$routes->resource('templates', ['controller' => 'TemplateController']);

// 添加不带版本号的API路由（为了兼容前端调用）
$routes->group('api', ['namespace' => 'App\Controllers'], function($routes) {
    // 模板管理相关 API
    $routes->group('templates', function($routes) {
        // 工作流定义
        $routes->get('workflows', 'TemplateController::getWorkflowDefinitions');     // 获取工作流定义列表
        $routes->post('workflows', 'TemplateController::createWorkflowDefinition');  // 创建工作流定义
        $routes->get('workflows/(:num)', 'TemplateController::getWorkflowDefinition/$1'); // 获取工作流定义详情
        $routes->put('workflows/(:num)', 'TemplateController::updateWorkflowDefinition/$1'); // 更新工作流定义
        $routes->delete('workflows/(:num)', 'TemplateController::deleteWorkflowDefinition/$1'); // 删除工作流定义
    });
});

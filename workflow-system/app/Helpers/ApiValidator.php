<?php

namespace App\Helpers;

class ApiValidator
{
    /**
     * 验证项目创建数据
     * 
     * @param array $data 要验证的数据
     * @return array 验证结果
     */
    public static function validateProjectCreate(array $data): array
    {
        $errors = [];

        // 必填字段验证
        $requiredFields = [
            'title' => '项目标题',
            'product_line_id' => '产品线ID',
            'sales_owner_uid' => '销售负责人UID',
            'main_department_id' => '主部门ID',
            'main_department_manager_uid' => '主部门负责人UID',
            'main_executor_uid' => '主部门执行人UID'
        ];

        foreach ($requiredFields as $field => $fieldName) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $errors[] = "{$fieldName}不能为空";
            }
        }

        // 数据类型验证
        if (isset($data['product_line_id']) && !is_numeric($data['product_line_id'])) {
            $errors[] = '产品线ID必须是数字';
        }

        if (isset($data['main_department_id']) && !is_numeric($data['main_department_id'])) {
            $errors[] = '主部门ID必须是数字';
        }

        // 字符串长度验证
        if (isset($data['title']) && mb_strlen($data['title']) > 100) {
            $errors[] = '项目标题不能超过100个字符';
        }

        if (isset($data['sales_owner_uid']) && strlen($data['sales_owner_uid']) > 16) {
            $errors[] = '销售负责人UID不能超过16个字符';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * 验证项目更新数据
     * 
     * @param array $data 要验证的数据
     * @return array 验证结果
     */
    public static function validateProjectUpdate(array $data): array
    {
        $errors = [];

        // 可选字段验证
        if (isset($data['title'])) {
            if (empty($data['title'])) {
                $errors[] = '项目标题不能为空';
            } elseif (mb_strlen($data['title']) > 100) {
                $errors[] = '项目标题不能超过100个字符';
            }
        }

        if (isset($data['main_department_manager_uid']) && strlen($data['main_department_manager_uid']) > 16) {
            $errors[] = '主部门负责人UID不能超过16个字符';
        }

        if (isset($data['main_executor_uid']) && strlen($data['main_executor_uid']) > 16) {
            $errors[] = '主部门执行人UID不能超过16个字符';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * 验证协同部门数据
     * 
     * @param array $data 要验证的数据
     * @return array 验证结果
     */
    public static function validateCollaboration(array $data): array
    {
        $errors = [];

        // 必填字段验证
        $requiredFields = [
            'department_id' => '协同部门ID',
            'manager_uid' => '协同部门负责人UID',
            'executor_uid' => '协同执行人UID',
            'analyst_uid' => '数据分析师UID'
        ];

        foreach ($requiredFields as $field => $fieldName) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $errors[] = "{$fieldName}不能为空";
            }
        }

        // 数据类型验证
        if (isset($data['department_id']) && !is_numeric($data['department_id'])) {
            $errors[] = '协同部门ID必须是数字';
        }

        // UID长度验证
        $uidFields = ['manager_uid', 'executor_uid', 'analyst_uid'];
        foreach ($uidFields as $field) {
            if (isset($data[$field]) && strlen($data[$field]) > 16) {
                $errors[] = ucfirst($field) . '不能超过16个字符';
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * 验证工作流操作数据
     * 
     * @param array $data 要验证的数据
     * @param string $actionType 操作类型
     * @return array 验证结果
     */
    public static function validateWorkflowAction(array $data, string $actionType): array
    {
        $errors = [];

        // 通用必填字段
        $commonFields = [
            'project_id' => '项目ID',
            'node_template_id' => '节点模板ID'
        ];

        foreach ($commonFields as $field => $fieldName) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $errors[] = "{$fieldName}不能为空";
            }
        }

        // 根据操作类型验证特定字段
        switch ($actionType) {
            case 'reject':
                if (!isset($data['reason']) || empty($data['reason'])) {
                    $errors[] = '驳回原因不能为空';
                }
                if (isset($data['reason']) && mb_strlen($data['reason']) > 500) {
                    $errors[] = '驳回原因不能超过500个字符';
                }
                break;

            case 'transition':
                if (!isset($data['to_node_template_id']) || empty($data['to_node_template_id'])) {
                    $errors[] = '目标节点模板ID不能为空';
                }
                break;

            case 'skip':
                // 跳过操作可选验证
                if (isset($data['reason']) && mb_strlen($data['reason']) > 500) {
                    $errors[] = '跳过原因不能超过500个字符';
                }
                break;
        }

        // 数据类型验证
        $numericFields = ['project_id', 'node_template_id', 'collaboration_id', 'to_node_template_id'];
        foreach ($numericFields as $field) {
            if (isset($data[$field]) && !is_numeric($data[$field])) {
                $errors[] = ucfirst($field) . '必须是数字';
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * 验证节点模板数据
     * 
     * @param array $data 要验证的数据
     * @return array 验证结果
     */
    public static function validateNodeTemplate(array $data): array
    {
        $errors = [];

        // 必填字段验证
        $requiredFields = [
            'node_code' => '节点编码',
            'node_name' => '节点名称',
            'assignee_role_id' => '处理角色ID',
            'node_type' => '节点类型'
        ];

        foreach ($requiredFields as $field => $fieldName) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $errors[] = "{$fieldName}不能为空";
            }
        }

        // 字符串长度验证
        if (isset($data['node_code']) && strlen($data['node_code']) > 32) {
            $errors[] = '节点编码不能超过32个字符';
        }

        if (isset($data['node_name']) && mb_strlen($data['node_name']) > 50) {
            $errors[] = '节点名称不能超过50个字符';
        }

        // 数据类型验证
        if (isset($data['assignee_role_id']) && !is_numeric($data['assignee_role_id'])) {
            $errors[] = '处理角色ID必须是数字';
        }

        if (isset($data['node_type']) && !in_array($data['node_type'], [1, 2])) {
            $errors[] = '节点类型必须是1（主流程）或2（协同流程）';
        }

        if (isset($data['is_optional']) && !in_array($data['is_optional'], [0, 1])) {
            $errors[] = '是否可跳过必须是0或1';
        }

        // 节点编码格式验证
        if (isset($data['node_code']) && !preg_match('/^[a-zA-Z0-9_]+$/', $data['node_code'])) {
            $errors[] = '节点编码只能包含字母、数字和下划线';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * 验证流转规则数据
     * 
     * @param array $data 要验证的数据
     * @return array 验证结果
     */
    public static function validateFlowRule(array $data): array
    {
        $errors = [];

        // 必填字段验证
        $requiredFields = [
            'product_line_id' => '产品线ID',
            'from_node_template_id' => '来源节点模板ID',
            'to_node_template_id' => '目标节点模板ID',
            'condition_type' => '条件类型',
            'condition_value' => '条件值'
        ];

        foreach ($requiredFields as $field => $fieldName) {
            if (!isset($data[$field]) || $data[$field] === '') {
                $errors[] = "{$fieldName}不能为空";
            }
        }

        // 数据类型验证
        $numericFields = ['product_line_id', 'from_node_template_id', 'to_node_template_id', 'priority'];
        foreach ($numericFields as $field) {
            if (isset($data[$field]) && !is_numeric($data[$field])) {
                $errors[] = ucfirst($field) . '必须是数字';
            }
        }

        // 字符串长度验证
        if (isset($data['condition_type']) && strlen($data['condition_type']) > 50) {
            $errors[] = '条件类型不能超过50个字符';
        }

        if (isset($data['condition_value']) && strlen($data['condition_value']) > 100) {
            $errors[] = '条件值不能超过100个字符';
        }

        if (isset($data['condition_expression']) && strlen($data['condition_expression']) > 200) {
            $errors[] = '条件表达式不能超过200个字符';
        }

        // 布尔值验证
        if (isset($data['is_active']) && !in_array($data['is_active'], [0, 1])) {
            $errors[] = '是否启用必须是0或1';
        }

        // 逻辑验证
        if (isset($data['from_node_template_id']) && isset($data['to_node_template_id']) 
            && $data['from_node_template_id'] == $data['to_node_template_id']) {
            $errors[] = '来源节点和目标节点不能相同';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * 验证用户数据
     * 
     * @param array $data 要验证的数据
     * @return array 验证结果
     */
    public static function validateUser(array $data): array
    {
        $errors = [];

        // 必填字段验证
        $requiredFields = [
            'user_uid' => '用户UID',
            'name' => '用户姓名',
            'department_id' => '部门ID',
            'role_id' => '角色ID'
        ];

        foreach ($requiredFields as $field => $fieldName) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $errors[] = "{$fieldName}不能为空";
            }
        }

        // 字符串长度验证
        if (isset($data['user_uid']) && strlen($data['user_uid']) > 16) {
            $errors[] = '用户UID不能超过16个字符';
        }

        if (isset($data['name']) && mb_strlen($data['name']) > 50) {
            $errors[] = '用户姓名不能超过50个字符';
        }

        // 数据类型验证
        if (isset($data['department_id']) && !is_numeric($data['department_id'])) {
            $errors[] = '部门ID必须是数字';
        }

        if (isset($data['role_id']) && !is_numeric($data['role_id'])) {
            $errors[] = '角色ID必须是数字';
        }

        // UID格式验证
        if (isset($data['user_uid']) && !preg_match('/^[a-zA-Z0-9_]+$/', $data['user_uid'])) {
            $errors[] = '用户UID只能包含字母、数字和下划线';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * 验证分页参数
     * 
     * @param array $params 分页参数
     * @return array 验证结果
     */
    public static function validatePagination(array $params): array
    {
        $errors = [];

        if (isset($params['page'])) {
            if (!is_numeric($params['page']) || $params['page'] < 1) {
                $errors[] = '页码必须是大于0的数字';
            }
        }

        if (isset($params['limit'])) {
            if (!is_numeric($params['limit']) || $params['limit'] < 1 || $params['limit'] > 100) {
                $errors[] = '每页数量必须是1-100之间的数字';
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * 验证状态值
     * 
     * @param mixed $status 状态值
     * @param array $validStatuses 有效状态列表
     * @return array 验证结果
     */
    public static function validateStatus($status, array $validStatuses): array
    {
        $errors = [];

        if (!is_numeric($status) || !in_array((int)$status, $validStatuses)) {
            $errors[] = '无效的状态值';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * 验证JSON数据
     * 
     * @param string $jsonString JSON字符串
     * @param string $fieldName 字段名称
     * @return array 验证结果
     */
    public static function validateJson(string $jsonString, string $fieldName): array
    {
        $errors = [];

        if (!empty($jsonString)) {
            json_decode($jsonString);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $errors[] = "{$fieldName}不是有效的JSON格式";
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
}
<?php

namespace App\Helpers;

class ErrorHandler
{
    /**
     * API错误码常量
     */
    const ERROR_CODES = [
        // 通用错误 1000-1999
        'INVALID_REQUEST' => 1001,
        'MISSING_PARAMETER' => 1002,
        'INVALID_PARAMETER' => 1003,
        'VALIDATION_FAILED' => 1004,
        'UNAUTHORIZED' => 1005,
        'FORBIDDEN' => 1006,
        'NOT_FOUND' => 1007,
        'METHOD_NOT_ALLOWED' => 1008,
        'INTERNAL_ERROR' => 1009,
        'DATABASE_ERROR' => 1010,
        
        // 项目相关错误 2000-2999
        'PROJECT_NOT_FOUND' => 2001,
        'PROJECT_ALREADY_EXISTS' => 2002,
        'PROJECT_STATUS_INVALID' => 2003,
        'PROJECT_CANNOT_DELETE' => 2004,
        'PROJECT_CREATE_FAILED' => 2005,
        'PROJECT_UPDATE_FAILED' => 2006,
        
        // 工作流相关错误 3000-3999
        'WORKFLOW_NOT_FOUND' => 3001,
        'WORKFLOW_ALREADY_STARTED' => 3002,
        'WORKFLOW_CANNOT_START' => 3003,
        'NODE_NOT_FOUND' => 3004,
        'NODE_STATUS_INVALID' => 3005,
        'NODE_CANNOT_OPERATE' => 3006,
        'TRANSITION_NOT_ALLOWED' => 3007,
        'WORKFLOW_ENGINE_ERROR' => 3008,
        
        // 协同相关错误 4000-4999
        'COLLABORATION_NOT_FOUND' => 4001,
        'COLLABORATION_ALREADY_EXISTS' => 4002,
        'COLLABORATION_CANNOT_CREATE' => 4003,
        'COLLABORATION_CANNOT_UPDATE' => 4004,
        'COLLABORATION_CANNOT_DELETE' => 4005,
        
        // 模板相关错误 5000-5999
        'TEMPLATE_NOT_FOUND' => 5001,
        'TEMPLATE_ALREADY_EXISTS' => 5002,
        'TEMPLATE_IN_USE' => 5003,
        'TEMPLATE_CREATE_FAILED' => 5004,
        'TEMPLATE_UPDATE_FAILED' => 5005,
        'TEMPLATE_DELETE_FAILED' => 5006,
        
        // 用户相关错误 6000-6999
        'USER_NOT_FOUND' => 6001,
        'USER_ALREADY_EXISTS' => 6002,
        'USER_CREATE_FAILED' => 6003,
        'USER_UPDATE_FAILED' => 6004,
        'USER_DELETE_FAILED' => 6005,
        'INSUFFICIENT_PERMISSION' => 6006,
        
        // 部门/角色相关错误 7000-7999
        'DEPARTMENT_NOT_FOUND' => 7001,
        'ROLE_NOT_FOUND' => 7002,
        'DEPARTMENT_ALREADY_EXISTS' => 7003,
        'ROLE_ALREADY_EXISTS' => 7004,
        
        // 产品线相关错误 8000-8999
        'PRODUCT_LINE_NOT_FOUND' => 8001,
        'PRODUCT_LINE_ALREADY_EXISTS' => 8002,
    ];

    /**
     * 错误消息映射
     */
    const ERROR_MESSAGES = [
        1001 => '无效的请求',
        1002 => '缺少必要参数',
        1003 => '参数格式错误',
        1004 => '数据验证失败',
        1005 => '未授权访问',
        1006 => '权限不足',
        1007 => '资源不存在',
        1008 => '请求方法不被允许',
        1009 => '服务器内部错误',
        1010 => '数据库操作失败',
        
        2001 => '项目不存在',
        2002 => '项目已存在',
        2003 => '项目状态无效',
        2004 => '项目无法删除',
        2005 => '项目创建失败',
        2006 => '项目更新失败',
        
        3001 => '工作流不存在',
        3002 => '工作流已启动',
        3003 => '工作流无法启动',
        3004 => '节点不存在',
        3005 => '节点状态无效',
        3006 => '节点无法操作',
        3007 => '不允许的流转',
        3008 => '工作流引擎错误',
        
        4001 => '协同不存在',
        4002 => '协同已存在',
        4003 => '无法创建协同',
        4004 => '无法更新协同',
        4005 => '无法删除协同',
        
        5001 => '模板不存在',
        5002 => '模板已存在',
        5003 => '模板正在使用中',
        5004 => '模板创建失败',
        5005 => '模板更新失败',
        5006 => '模板删除失败',
        
        6001 => '用户不存在',
        6002 => '用户已存在',
        6003 => '用户创建失败',
        6004 => '用户更新失败',
        6005 => '用户删除失败',
        6006 => '权限不足',
        
        7001 => '部门不存在',
        7002 => '角色不存在',
        7003 => '部门已存在',
        7004 => '角色已存在',
        
        8001 => '产品线不存在',
        8002 => '产品线已存在',
    ];

    /**
     * 创建错误响应
     * 
     * @param string $errorKey 错误键
     * @param string|null $customMessage 自定义错误消息
     * @param array $details 错误详情
     * @return array
     */
    public static function createError(string $errorKey, ?string $customMessage = null, array $details = []): array
    {
        $errorCode = self::ERROR_CODES[$errorKey] ?? self::ERROR_CODES['INTERNAL_ERROR'];
        $errorMessage = $customMessage ?? self::ERROR_MESSAGES[$errorCode] ?? '未知错误';

        $error = [
            'status' => 'error',
            'error_code' => $errorCode,
            'error_key' => $errorKey,
            'message' => $errorMessage,
            'timestamp' => time()
        ];

        if (!empty($details)) {
            $error['details'] = $details;
        }

        return $error;
    }

    /**
     * 创建验证错误响应
     * 
     * @param array $errors 验证错误列表
     * @return array
     */
    public static function createValidationError(array $errors): array
    {
        return self::createError('VALIDATION_FAILED', '数据验证失败', ['validation_errors' => $errors]);
    }

    /**
     * 根据HTTP状态码获取对应的错误键
     * 
     * @param int $httpCode HTTP状态码
     * @return string
     */
    public static function getErrorKeyByHttpCode(int $httpCode): string
    {
        $mapping = [
            400 => 'INVALID_REQUEST',
            401 => 'UNAUTHORIZED',
            403 => 'FORBIDDEN',
            404 => 'NOT_FOUND',
            405 => 'METHOD_NOT_ALLOWED',
            500 => 'INTERNAL_ERROR'
        ];

        return $mapping[$httpCode] ?? 'INTERNAL_ERROR';
    }

    /**
     * 记录错误日志
     * 
     * @param string $errorKey 错误键
     * @param string $message 错误消息
     * @param array $context 上下文信息
     * @param string $level 日志级别
     */
    public static function logError(string $errorKey, string $message, array $context = [], string $level = 'error'): void
    {
        $logMessage = "[{$errorKey}] {$message}";
        
        if (!empty($context)) {
            $logMessage .= ' Context: ' . json_encode($context, JSON_UNESCAPED_UNICODE);
        }

        switch ($level) {
            case 'debug':
                log_message('debug', $logMessage);
                break;
            case 'info':
                log_message('info', $logMessage);
                break;
            case 'warning':
                log_message('warning', $logMessage);
                break;
            case 'error':
            default:
                log_message('error', $logMessage);
                break;
        }
    }

    /**
     * 处理异常并创建错误响应
     * 
     * @param \Throwable $exception 异常对象
     * @param string $defaultErrorKey 默认错误键
     * @return array
     */
    public static function handleException(\Throwable $exception, string $defaultErrorKey = 'INTERNAL_ERROR'): array
    {
        // 记录异常日志
        self::logError($defaultErrorKey, $exception->getMessage(), [
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString()
        ]);

        // 在开发环境显示详细错误信息
        if (ENVIRONMENT === 'development') {
            return self::createError($defaultErrorKey, $exception->getMessage(), [
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => explode("\n", $exception->getTraceAsString())
            ]);
        }

        // 生产环境返回通用错误信息
        return self::createError($defaultErrorKey);
    }

    /**
     * 检查是否为数据库错误
     * 
     * @param \Throwable $exception 异常对象
     * @return bool
     */
    public static function isDatabaseError(\Throwable $exception): bool
    {
        $databaseErrorPatterns = [
            'database',
            'mysql',
            'connection',
            'sql',
            'query',
            'table',
            'column',
            'constraint'
        ];

        $message = strtolower($exception->getMessage());
        
        foreach ($databaseErrorPatterns as $pattern) {
            if (strpos($message, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * 格式化验证错误消息
     * 
     * @param array $validationErrors 验证错误
     * @return string
     */
    public static function formatValidationErrors(array $validationErrors): string
    {
        if (empty($validationErrors)) {
            return '';
        }

        return implode('; ', $validationErrors);
    }

    /**
     * 创建业务逻辑错误响应
     * 
     * @param string $message 错误消息
     * @param string $errorKey 错误键
     * @param array $context 上下文
     * @return array
     */
    public static function createBusinessError(string $message, string $errorKey = 'INTERNAL_ERROR', array $context = []): array
    {
        self::logError($errorKey, $message, $context, 'warning');
        return self::createError($errorKey, $message);
    }

    /**
     * 获取错误码对应的HTTP状态码
     * 
     * @param int $errorCode 错误码
     * @return int
     */
    public static function getHttpStatusByErrorCode(int $errorCode): int
    {
        // 1000-1999: 通用错误
        if ($errorCode >= 1000 && $errorCode < 2000) {
            switch ($errorCode) {
                case 1002:
                case 1003:
                case 1004:
                    return 400; // Bad Request
                case 1005:
                    return 401; // Unauthorized
                case 1006:
                    return 403; // Forbidden
                case 1007:
                    return 404; // Not Found
                case 1008:
                    return 405; // Method Not Allowed
                default:
                    return 500; // Internal Server Error
            }
        }

        // 2000及以上: 业务错误，通常返回400
        if ($errorCode >= 2000) {
            // 特殊情况：资源不存在返回404
            $notFoundCodes = [2001, 3001, 4001, 5001, 6001, 7001, 7002, 8001];
            if (in_array($errorCode, $notFoundCodes)) {
                return 404;
            }
            
            // 权限相关返回403
            $forbiddenCodes = [6006];
            if (in_array($errorCode, $forbiddenCodes)) {
                return 403;
            }
            
            return 400; // Bad Request
        }

        return 500; // Internal Server Error
    }
}
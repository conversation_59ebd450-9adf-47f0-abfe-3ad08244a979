<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\HTTP\CLIRequest;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\Response;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;
use App\Helpers\ApiValidator;
use App\Helpers\ErrorHandler;

/**
 * API基础控制器
 * 提供统一的API响应格式、错误处理、日志记录等功能
 */
abstract class BaseController extends Controller
{
    /**
     * Instance of the main Request object.
     *
     * @var CLIRequest|IncomingRequest
     */
    protected $request;

    /**
     * An array of helpers to be loaded automatically upon
     * class instantiation. These helpers will be available
     * to all other controllers that extend BaseController.
     *
     * @var array
     */
    protected $helpers = ['url', 'form'];

    /**
     * 默认响应格式
     */
    protected $format = 'json';

    /**
     * Constructor.
     */
    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        // Do Not Edit This Line
        parent::initController($request, $response, $logger);

        // Preload any models, libraries, etc, here.
        
        // 设置响应头
        $this->response->setHeader('Content-Type', 'application/json; charset=utf-8');
        $this->response->setHeader('Access-Control-Allow-Origin', '*');
        $this->response->setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        $this->response->setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    }

    /**
     * 成功响应
     *
     * @param mixed $data 响应数据
     * @param string $message 响应消息
     * @param int $code HTTP状态码
     * @return ResponseInterface
     */
    protected function respond($data = null, string $message = 'success', int $code = 200): ResponseInterface
    {
        $response = [
            'status' => 'success',
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ];

        return $this->response
                    ->setStatusCode($code)
                    ->setJSON($response);
    }

    /**
     * 分页数据响应
     *
     * @param array $data 数据列表
     * @param int $total 总数量
     * @param int $page 当前页码
     * @param int $limit 每页数量
     * @param string $message 响应消息
     * @return ResponseInterface
     */
    protected function respondWithPagination(
        array $data, 
        int $total, 
        int $page = 1, 
        int $limit = 20, 
        string $message = 'success'
    ): ResponseInterface {
        $totalPages = ceil($total / $limit);
        
        $response = [
            'status' => 'success',
            'code' => 200,
            'message' => $message,
            'data' => [
                'list' => $data,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $total,
                    'total_pages' => $totalPages,
                    'has_next' => $page < $totalPages,
                    'has_prev' => $page > 1
                ]
            ],
            'timestamp' => time()
        ];

        return $this->response->setJSON($response);
    }

    /**
     * 错误响应
     *
     * @param string $message 错误消息
     * @param int $code HTTP状态码
     * @param mixed $errors 详细错误信息
     * @return ResponseInterface
     */
    protected function respondWithError(string $message = 'error', int $code = 400, $errors = null): ResponseInterface
    {
        $response = [
            'status' => 'error',
            'code' => $code,
            'message' => $message,
            'errors' => $errors,
            'timestamp' => time()
        ];

        // 记录错误日志
        log_message('error', '[API Error] ' . $message . ' | Code: ' . $code . ' | Errors: ' . json_encode($errors));

        return $this->response
                    ->setStatusCode($code)
                    ->setJSON($response);
    }

    /**
     * 验证失败响应
     *
     * @param array $errors 验证错误信息
     * @return ResponseInterface
     */
    protected function respondWithValidationError(array $errors): ResponseInterface
    {
        return $this->respondWithError('数据验证失败', 422, $errors);
    }

    /**
     * 未授权响应
     *
     * @param string $message 错误消息
     * @return ResponseInterface
     */
    protected function respondWithUnauthorized(string $message = '未授权访问'): ResponseInterface
    {
        return $this->respondWithError($message, 401);
    }

    /**
     * 禁止访问响应
     *
     * @param string $message 错误消息
     * @return ResponseInterface
     */
    protected function respondWithForbidden(string $message = '禁止访问'): ResponseInterface
    {
        return $this->respondWithError($message, 403);
    }

    /**
     * 资源未找到响应
     *
     * @param string $message 错误消息
     * @return ResponseInterface
     */
    protected function respondWithNotFound(string $message = '资源未找到'): ResponseInterface
    {
        return $this->respondWithError($message, 404);
    }

    /**
     * 内部服务器错误响应
     *
     * @param string $message 错误消息
     * @param \Exception|null $exception 异常对象
     * @return ResponseInterface
     */
    protected function respondWithServerError(string $message = '内部服务器错误', \Exception $exception = null): ResponseInterface
    {
        if ($exception) {
            log_message('critical', '[API Exception] ' . $exception->getMessage() . ' | File: ' . $exception->getFile() . ' | Line: ' . $exception->getLine());
        }

        return $this->respondWithError($message, 500);
    }

    /**
     * 获取请求参数
     *
     * @param string $key 参数名
     * @param mixed $default 默认值
     * @return mixed
     */
    protected function getInput(string $key, $default = null)
    {
        $input = $this->request->getPost($key);
        if ($input === null) {
            $input = $this->request->getGet($key);
        }
        if ($input === null) {
            $input = $this->request->getJSON(true)[$key] ?? null;
        }
        
        return $input ?? $default;
    }

    /**
     * 获取所有请求参数
     *
     * @return array
     */
    protected function getAllInput(): array
    {
        $get = $this->request->getGet() ?? [];
        $post = $this->request->getPost() ?? [];
        $json = $this->request->getJSON(true) ?? [];
        
        return array_merge($get, $post, $json);
    }

    /**
     * 验证必需参数
     *
     * @param array $required 必需参数列表
     * @param array $data 数据数组
     * @return array|null 验证错误信息，null表示验证通过
     */
    protected function validateRequired(array $required, array $data): ?array
    {
        $errors = [];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || $data[$field] === '' || $data[$field] === null) {
                $errors[$field] = "参数 {$field} 是必需的";
            }
        }
        
        return empty($errors) ? null : $errors;
    }

    /**
     * 记录操作日志
     *
     * @param string $action 操作动作
     * @param array $data 相关数据
     * @param string $userUid 操作用户ID
     */
    protected function logAction(string $action, array $data = [], string $userUid = ''): void
    {
        $logData = [
            'action' => $action,
            'user_uid' => $userUid,
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent(),
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        log_message('info', '[API Action] ' . json_encode($logData));
    }

    // ==================== 错误处理辅助方法 ====================

    /**
     * 使用ErrorHandler创建错误响应
     *
     * @param string $errorKey 错误键
     * @param string|null $customMessage 自定义错误消息
     * @param array $details 错误详情
     * @return ResponseInterface
     */
    protected function failWithError(string $errorKey, ?string $customMessage = null, array $details = []): ResponseInterface
    {
        $error = ErrorHandler::createError($errorKey, $customMessage, $details);
        $httpCode = ErrorHandler::getHttpStatusByErrorCode($error['error_code']);
        
        return $this->response
                    ->setStatusCode($httpCode)
                    ->setJSON($error);
    }

    /**
     * 验证失败响应（使用ErrorHandler）
     *
     * @param array $errors 验证错误列表
     * @return ResponseInterface
     */
    protected function failValidationErrors(array $errors): ResponseInterface
    {
        $error = ErrorHandler::createValidationError($errors);
        $httpCode = ErrorHandler::getHttpStatusByErrorCode($error['error_code']);
        
        return $this->response
                    ->setStatusCode($httpCode)
                    ->setJSON($error);
    }

    /**
     * 处理异常并返回错误响应
     *
     * @param \Throwable $exception 异常对象
     * @param string $defaultErrorKey 默认错误键
     * @return ResponseInterface
     */
    protected function failWithException(\Throwable $exception, string $defaultErrorKey = 'INTERNAL_ERROR'): ResponseInterface
    {
        $error = ErrorHandler::handleException($exception, $defaultErrorKey);
        $httpCode = ErrorHandler::getHttpStatusByErrorCode($error['error_code']);
        
        return $this->response
                    ->setStatusCode($httpCode)
                    ->setJSON($error);
    }

    // ==================== 快捷错误响应方法 ====================

    /**
     * 参数验证失败响应
     */
    protected function failValidationError(string $message): ResponseInterface
    {
        return $this->failWithError('VALIDATION_FAILED', $message);
    }

    /**
     * 资源未找到响应
     */
    protected function failNotFound(string $message = '资源不存在'): ResponseInterface
    {
        return $this->failWithError('NOT_FOUND', $message);
    }

    /**
     * 未授权响应
     */
    protected function failUnauthorized(string $message = '未授权访问'): ResponseInterface
    {
        return $this->failWithError('UNAUTHORIZED', $message);
    }

    /**
     * 权限不足响应
     */
    protected function failForbidden(string $message = '权限不足'): ResponseInterface
    {
        return $this->failWithError('FORBIDDEN', $message);
    }

    /**
     * 服务器错误响应
     */
    protected function failServerError(string $message = '服务器内部错误'): ResponseInterface
    {
        return $this->failWithError('INTERNAL_ERROR', $message);
    }

    /**
     * 创建成功响应
     */
    protected function respondCreated($data = null, string $message = '创建成功'): ResponseInterface
    {
        return $this->respond($data, $message, 201);
    }

    // ==================== 数据验证辅助方法 ====================

    /**
     * 验证项目创建数据
     *
     * @param array $data 数据
     * @return bool|ResponseInterface 验证通过返回true，失败返回错误响应
     */
    protected function validateProjectCreate(array $data)
    {
        $validation = ApiValidator::validateProjectCreate($data);
        if (!$validation['valid']) {
            return $this->failValidationErrors($validation['errors']);
        }
        return true;
    }

    /**
     * 验证工作流操作数据
     *
     * @param array $data 数据
     * @param string $actionType 操作类型
     * @return bool|ResponseInterface 验证通过返回true，失败返回错误响应
     */
    protected function validateWorkflowAction(array $data, string $actionType)
    {
        $validation = ApiValidator::validateWorkflowAction($data, $actionType);
        if (!$validation['valid']) {
            return $this->failValidationErrors($validation['errors']);
        }
        return true;
    }

    /**
     * 验证分页参数
     *
     * @param array $params 参数
     * @return bool|ResponseInterface 验证通过返回true，失败返回错误响应
     */
    protected function validatePagination(array $params)
    {
        $validation = ApiValidator::validatePagination($params);
        if (!$validation['valid']) {
            return $this->failValidationErrors($validation['errors']);
        }
        return true;
    }
}


<?php

namespace App\Controllers;

use App\Models\ProjectModel;
use App\Models\ProductLineModel;
use App\Models\DepartmentModel;
use App\Models\UserModel;
use App\Libraries\WorkflowEngine;

/**
 * 项目管理控制器
 * 处理项目的创建、查询、更新、删除等操作
 */
class ProjectController extends BaseController
{
    protected $projectModel;
    protected $productLineModel;
    protected $departmentModel;
    protected $userModel;
    protected $workflowEngine;

    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->productLineModel = new ProductLineModel();
        $this->departmentModel = new DepartmentModel();
        $this->userModel = new UserModel();
        $this->workflowEngine = new WorkflowEngine();
    }

    /**
     * 获取项目列表
     * GET /api/projects
     */
    public function index()
    {
        try {
            $page = (int)$this->getInput('page', 1);
            $limit = (int)$this->getInput('limit', 20);
            $status = $this->getInput('status');
            $productLineId = $this->getInput('product_line_id');
            $userUid = $this->getInput('user_uid');

            // 构建查询条件
            $where = ['deleted_at' => 0];
            if ($status !== null) {
                $where['status'] = $status;
            }
            if ($productLineId) {
                $where['product_line_id'] = $productLineId;
            }

            $builder = $this->projectModel->select('project.*, 
                                product_line.name as product_line_name,
                                product_line.code as product_line_code,
                                department.name as main_department_name,
                                node_template.node_name as current_node_name')
                        ->join('product_line', 'product_line.id = project.product_line_id', 'left')
                        ->join('department', 'department.id = project.main_department_id', 'left')
                        ->join('node_template', 'node_template.id = project.current_node_template_id', 'left')
                        ->where($where);

            // 如果指定了用户，只查询相关项目
            if ($userUid) {
                $builder->groupStart()
                        ->where('sales_owner_uid', $userUid)
                        ->orWhere('main_department_manager_uid', $userUid)
                        ->orWhere('main_executor_uid', $userUid)
                        ->groupEnd();
            }

            // 分页查询
            $total = $builder->countAllResults(false);
            $projects = $builder->orderBy('project.created_at', 'DESC')
                               ->limit($limit, ($page - 1) * $limit)
                               ->get()
                               ->getResultArray();

            return $this->respondWithPagination($projects, $total, $page, $limit, '获取项目列表成功');

        } catch (\Exception $e) {
            return $this->respondWithServerError('获取项目列表失败', $e);
        }
    }

    /**
     * 获取项目详情
     * GET /api/projects/{id}
     */
    public function show($id)
    {
        try {
            $project = $this->projectModel->getProjectWithDetails($id);
            
            if (!$project) {
                return $this->respondWithNotFound('项目不存在');
            }

            return $this->respond($project, '获取项目详情成功');

        } catch (\Exception $e) {
            return $this->respondWithServerError('获取项目详情失败', $e);
        }
    }

    /**
     * 创建项目
     * POST /api/projects
     */
    public function create()
    {
        try {
            $data = $this->getAllInput();

            // 验证必需参数
            $required = ['title', 'product_line_id', 'sales_owner_uid'];
            $errors = $this->validateRequired($required, $data);
            if ($errors) {
                return $this->respondWithValidationError($errors);
            }

            // 验证产品线是否存在
            $productLine = $this->productLineModel->find($data['product_line_id']);
            if (!$productLine) {
                return $this->respondWithError('产品线不存在', 400);
            }

            // 验证销售人员是否存在
            $salesOwner = $this->userModel->getUserByUid($data['sales_owner_uid']);
            if (!$salesOwner) {
                return $this->respondWithError('销售人员不存在', 400);
            }

            // 根据产品线自动分配主部门（D产品线需要手动选择）
            if ($productLine['code'] === 'D') {
                if (empty($data['main_department_id'])) {
                    return $this->respondWithError('D产品线需要手动选择主部门', 400);
                }
            } else {
                // 其他产品线自动分配
                $mainDepartment = $this->departmentModel->getMainDepartmentByProductLine($productLine['code']);
                if ($mainDepartment) {
                    $data['main_department_id'] = $mainDepartment['id'];
                }
            }

            // 验证主部门
            if (empty($data['main_department_id'])) {
                return $this->respondWithError('无法确定主部门', 400);
            }

            $department = $this->departmentModel->find($data['main_department_id']);
            if (!$department || !$department['is_main_dept']) {
                return $this->respondWithError('指定的主部门无效', 400);
            }

            // 获取主部门负责人
            $deptManager = $this->userModel->getDepartmentManager($data['main_department_id']);
            if ($deptManager) {
                $data['main_department_manager_uid'] = $deptManager['user_uid'];
            }

            // 使用工作流引擎创建项目
            $projectId = $this->workflowEngine->createProject($data, $data['sales_owner_uid']);

            $this->logAction('create_project', ['project_id' => $projectId], $data['sales_owner_uid']);

            return $this->respond(['project_id' => $projectId], '创建项目成功', 201);

        } catch (\Exception $e) {
            return $this->respondWithServerError('创建项目失败', $e);
        }
    }

    /**
     * 更新项目
     * PUT /api/projects/{id}
     */
    public function update($id)
    {
        try {
            $project = $this->projectModel->find($id);
            if (!$project) {
                return $this->respondWithNotFound('项目不存在');
            }

            $data = $this->getAllInput();
            $updaterUid = $this->getInput('updater_uid');

            if (!$updaterUid) {
                return $this->respondWithError('缺少更新人信息', 400);
            }

            // 只允许更新特定字段
            $allowedFields = ['title', 'sign_status'];
            $updateData = [];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }

            if (empty($updateData)) {
                return $this->respondWithError('没有需要更新的数据', 400);
            }

            $updateData['updater'] = $updaterUid;
            $result = $this->projectModel->update($id, $updateData);

            if (!$result) {
                return $this->respondWithError('更新项目失败', 500);
            }

            $this->logAction('update_project', ['project_id' => $id, 'data' => $updateData], $updaterUid);

            return $this->respond(null, '更新项目成功');

        } catch (\Exception $e) {
            return $this->respondWithServerError('更新项目失败', $e);
        }
    }

    /**
     * 删除项目（软删除）
     * DELETE /api/projects/{id}
     */
    public function delete($id)
    {
        try {
            $project = $this->projectModel->find($id);
            if (!$project) {
                return $this->respondWithNotFound('项目不存在');
            }

            $deleterUid = $this->getInput('deleter_uid');
            if (!$deleterUid) {
                return $this->respondWithError('缺少删除人信息', 400);
            }

            $result = $this->projectModel->update($id, [
                'deleted_at' => time(),
                'deleter' => $deleterUid
            ]);

            if (!$result) {
                return $this->respondWithError('删除项目失败', 500);
            }

            $this->logAction('delete_project', ['project_id' => $id], $deleterUid);

            return $this->respond(null, '删除项目成功');

        } catch (\Exception $e) {
            return $this->respondWithServerError('删除项目失败', $e);
        }
    }
}
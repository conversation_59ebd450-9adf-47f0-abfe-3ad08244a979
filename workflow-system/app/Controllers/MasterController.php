<?php

namespace App\Controllers;

use App\Models\ProductLineModel;
use App\Models\DepartmentModel;
use App\Models\RoleModel;
use App\Models\UserModel;
use CodeIgniter\HTTP\ResponseInterface;

class MasterController extends BaseController
{
    protected $productLineModel;
    protected $departmentModel;
    protected $roleModel;
    protected $userModel;

    public function __construct()
    {
        $this->productLineModel = new ProductLineModel();
        $this->departmentModel = new DepartmentModel();
        $this->roleModel = new RoleModel();
        $this->userModel = new UserModel();
    }

    // ==================== 产品线管理 ====================

    /**
     * 获取产品线列表
     */
    public function getProductLines()
    {
        try {
            $productLines = $this->productLineModel->getAllActive();
            return $this->respond($productLines, '获取产品线列表成功');
        } catch (\Exception $e) {
            return $this->failServerError('获取产品线列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建产品线
     */
    public function createProductLine()
    {
        try {
            $data = $this->request->getJSON(true);
            
            if (!isset($data['name']) || empty($data['name'])) {
                return $this->failValidationError('产品线名称不能为空');
            }

            $productLineData = [
                'name' => $data['name'],
                'description' => $data['description'] ?? '',
                'creator' => $this->getCurrentUserId()
            ];

            $productLineId = $this->productLineModel->insert($productLineData);
            
            if (!$productLineId) {
                return $this->failServerError('创建产品线失败');
            }

            return $this->respondCreated(['id' => $productLineId], '产品线创建成功');
        } catch (\Exception $e) {
            return $this->failServerError('创建产品线失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取产品线详情
     */
    public function getProductLine($id)
    {
        try {
            $productLine = $this->productLineModel->find($id);
            
            if (!$productLine) {
                return $this->failNotFound('产品线不存在');
            }

            return $this->respond($productLine, '获取产品线详情成功');
        } catch (\Exception $e) {
            return $this->failServerError('获取产品线详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新产品线
     */
    public function updateProductLine($id)
    {
        try {
            $data = $this->request->getJSON(true);
            
            $productLine = $this->productLineModel->find($id);
            if (!$productLine) {
                return $this->failNotFound('产品线不存在');
            }

            $updateData = [
                'updater' => $this->getCurrentUserId()
            ];

            if (isset($data['name'])) {
                $updateData['name'] = $data['name'];
            }
            if (isset($data['description'])) {
                $updateData['description'] = $data['description'];
            }

            $result = $this->productLineModel->update($id, $updateData);
            
            if (!$result) {
                return $this->failServerError('更新产品线失败');
            }

            return $this->respond(null, '产品线更新成功');
        } catch (\Exception $e) {
            return $this->failServerError('更新产品线失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除产品线
     */
    public function deleteProductLine($id)
    {
        try {
            $productLine = $this->productLineModel->find($id);
            if (!$productLine) {
                return $this->failNotFound('产品线不存在');
            }

            $result = $this->productLineModel->update($id, [
                'deleted_at' => time(),
                'deleter' => $this->getCurrentUserId()
            ]);
            
            if (!$result) {
                return $this->failServerError('删除产品线失败');
            }

            return $this->respond(null, '产品线删除成功');
        } catch (\Exception $e) {
            return $this->failServerError('删除产品线失败: ' . $e->getMessage());
        }
    }

    // ==================== 部门管理 ====================

    /**
     * 获取部门列表
     */
    public function getDepartments()
    {
        try {
            $departments = $this->departmentModel->getAllActive();
            return $this->respond($departments, '获取部门列表成功');
        } catch (\Exception $e) {
            return $this->failServerError('获取部门列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建部门
     */
    public function createDepartment()
    {
        try {
            $data = $this->request->getJSON(true);
            
            if (!isset($data['name']) || empty($data['name'])) {
                return $this->failValidationError('部门名称不能为空');
            }

            $departmentData = [
                'name' => $data['name'],
                'creator' => $this->getCurrentUserId()
            ];

            $departmentId = $this->departmentModel->insert($departmentData);
            
            if (!$departmentId) {
                return $this->failServerError('创建部门失败');
            }

            return $this->respondCreated(['id' => $departmentId], '部门创建成功');
        } catch (\Exception $e) {
            return $this->failServerError('创建部门失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取部门详情
     */
    public function getDepartment($id)
    {
        try {
            $department = $this->departmentModel->find($id);
            
            if (!$department) {
                return $this->failNotFound('部门不存在');
            }

            return $this->respond($department, '获取部门详情成功');
        } catch (\Exception $e) {
            return $this->failServerError('获取部门详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新部门
     */
    public function updateDepartment($id)
    {
        try {
            $data = $this->request->getJSON(true);
            
            $department = $this->departmentModel->find($id);
            if (!$department) {
                return $this->failNotFound('部门不存在');
            }

            $updateData = [
                'updater' => $this->getCurrentUserId()
            ];

            if (isset($data['name'])) {
                $updateData['name'] = $data['name'];
            }

            $result = $this->departmentModel->update($id, $updateData);
            
            if (!$result) {
                return $this->failServerError('更新部门失败');
            }

            return $this->respond(null, '部门更新成功');
        } catch (\Exception $e) {
            return $this->failServerError('更新部门失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除部门
     */
    public function deleteDepartment($id)
    {
        try {
            $department = $this->departmentModel->find($id);
            if (!$department) {
                return $this->failNotFound('部门不存在');
            }

            $result = $this->departmentModel->update($id, [
                'deleted_at' => time(),
                'deleter' => $this->getCurrentUserId()
            ]);
            
            if (!$result) {
                return $this->failServerError('删除部门失败');
            }

            return $this->respond(null, '部门删除成功');
        } catch (\Exception $e) {
            return $this->failServerError('删除部门失败: ' . $e->getMessage());
        }
    }

    // ==================== 角色管理 ====================

    /**
     * 获取角色列表
     */
    public function getRoles()
    {
        try {
            $roles = $this->roleModel->getAllActive();
            return $this->respond($roles, '获取角色列表成功');
        } catch (\Exception $e) {
            return $this->failServerError('获取角色列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建角色
     */
    public function createRole()
    {
        try {
            $data = $this->request->getJSON(true);
            
            if (!isset($data['name']) || empty($data['name'])) {
                return $this->failValidationError('角色名称不能为空');
            }

            $roleData = [
                'name' => $data['name'],
                'creator' => $this->getCurrentUserId()
            ];

            $roleId = $this->roleModel->insert($roleData);
            
            if (!$roleId) {
                return $this->failServerError('创建角色失败');
            }

            return $this->respondCreated(['id' => $roleId], '角色创建成功');
        } catch (\Exception $e) {
            return $this->failServerError('创建角色失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取角色详情
     */
    public function getRole($id)
    {
        try {
            $role = $this->roleModel->find($id);
            
            if (!$role) {
                return $this->failNotFound('角色不存在');
            }

            return $this->respond($role, '获取角色详情成功');
        } catch (\Exception $e) {
            return $this->failServerError('获取角色详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新角色
     */
    public function updateRole($id)
    {
        try {
            $data = $this->request->getJSON(true);
            
            $role = $this->roleModel->find($id);
            if (!$role) {
                return $this->failNotFound('角色不存在');
            }

            $updateData = [
                'updater' => $this->getCurrentUserId()
            ];

            if (isset($data['name'])) {
                $updateData['name'] = $data['name'];
            }

            $result = $this->roleModel->update($id, $updateData);
            
            if (!$result) {
                return $this->failServerError('更新角色失败');
            }

            return $this->respond(null, '角色更新成功');
        } catch (\Exception $e) {
            return $this->failServerError('更新角色失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除角色
     */
    public function deleteRole($id)
    {
        try {
            $role = $this->roleModel->find($id);
            if (!$role) {
                return $this->failNotFound('角色不存在');
            }

            $result = $this->roleModel->update($id, [
                'deleted_at' => time(),
                'deleter' => $this->getCurrentUserId()
            ]);
            
            if (!$result) {
                return $this->failServerError('删除角色失败');
            }

            return $this->respond(null, '角色删除成功');
        } catch (\Exception $e) {
            return $this->failServerError('删除角色失败: ' . $e->getMessage());
        }
    }

    // ==================== 用户管理 ====================

    /**
     * 获取用户列表
     */
    public function getUsers()
    {
        try {
            $page = $this->request->getGet('page') ?? 1;
            $limit = $this->request->getGet('limit') ?? 20;
            $departmentId = $this->request->getGet('department_id');
            $roleId = $this->request->getGet('role_id');

            $users = $this->userModel->getUsers($page, $limit, $departmentId, $roleId);
            return $this->respond($users, '获取用户列表成功');
        } catch (\Exception $e) {
            return $this->failServerError('获取用户列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建用户
     */
    public function createUser()
    {
        try {
            $data = $this->request->getJSON(true);
            
            // 验证必填字段
            $requiredFields = ['user_uid', 'name', 'department_id', 'role_id'];
            foreach ($requiredFields as $field) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    return $this->failValidationError("字段 {$field} 不能为空");
                }
            }

            $userData = [
                'user_uid' => $data['user_uid'],
                'name' => $data['name'],
                'department_id' => $data['department_id'],
                'role_id' => $data['role_id'],
                'creator' => $this->getCurrentUserId()
            ];

            $userId = $this->userModel->insert($userData);
            
            if (!$userId) {
                return $this->failServerError('创建用户失败');
            }

            return $this->respondCreated(['id' => $userId], '用户创建成功');
        } catch (\Exception $e) {
            return $this->failServerError('创建用户失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取用户详情
     */
    public function getUser($userUid)
    {
        try {
            $user = $this->userModel->getUserByUid($userUid);
            
            if (!$user) {
                return $this->failNotFound('用户不存在');
            }

            return $this->respond($user, '获取用户详情成功');
        } catch (\Exception $e) {
            return $this->failServerError('获取用户详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新用户
     */
    public function updateUser($userUid)
    {
        try {
            $data = $this->request->getJSON(true);
            
            $user = $this->userModel->where('user_uid', $userUid)->first();
            if (!$user) {
                return $this->failNotFound('用户不存在');
            }

            $updateData = [
                'updater' => $this->getCurrentUserId()
            ];

            $allowedFields = ['name', 'department_id', 'role_id'];
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }

            $result = $this->userModel->update($user['id'], $updateData);
            
            if (!$result) {
                return $this->failServerError('更新用户失败');
            }

            return $this->respond(null, '用户更新成功');
        } catch (\Exception $e) {
            return $this->failServerError('更新用户失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除用户
     */
    public function deleteUser($userUid)
    {
        try {
            $user = $this->userModel->where('user_uid', $userUid)->first();
            if (!$user) {
                return $this->failNotFound('用户不存在');
            }

            $result = $this->userModel->update($user['id'], [
                'deleted_at' => time(),
                'deleter' => $this->getCurrentUserId()
            ]);
            
            if (!$result) {
                return $this->failServerError('删除用户失败');
            }

            return $this->respond(null, '用户删除成功');
        } catch (\Exception $e) {
            return $this->failServerError('删除用户失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取部门用户
     */
    public function getDepartmentUsers($departmentId)
    {
        try {
            $users = $this->userModel->getUsersByDepartment($departmentId);
            return $this->respond($users, '获取部门用户成功');
        } catch (\Exception $e) {
            return $this->failServerError('获取部门用户失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取角色用户
     */
    public function getRoleUsers($roleId)
    {
        try {
            $users = $this->userModel->getUsersByRole($roleId);
            return $this->respond($users, '获取角色用户成功');
        } catch (\Exception $e) {
            return $this->failServerError('获取角色用户失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取当前用户ID（临时实现）
     */
    private function getCurrentUserId()
    {
        // 这里应该从认证系统获取当前用户ID
        // 临时返回一个默认值
        return 'system';
    }
}
<?php

namespace App\Controllers;

use App\Models\ProjectModel;
use App\Models\ProjectNodeInstanceModel;
use App\Models\ProjectCollaborationModel;
use App\Models\WorkflowDefinitionModel;
use App\Models\NodeTemplateModel;
use App\Libraries\WorkflowEngine;

/**
 * 工作流控制器
 * 处理项目流程流转、协同部门管理、节点操作等
 */
class WorkflowController extends BaseController
{
    protected $projectModel;
    protected $nodeInstanceModel;
    protected $collaborationModel;
    protected $workflowDefinitionModel;
    protected $nodeTemplateModel;
    protected $workflowEngine;

    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->nodeInstanceModel = new ProjectNodeInstanceModel();
        $this->collaborationModel = new ProjectCollaborationModel();
        $this->workflowDefinitionModel = new WorkflowDefinitionModel();
        $this->nodeTemplateModel = new NodeTemplateModel();
        $this->workflowEngine = new WorkflowEngine();
    }

    /**
     * 获取项目流程状态
     * GET /api/workflow/{projectId}/status
     */
    public function getProjectStatus($projectId)
    {
        try {
            $project = $this->projectModel->getProjectWithDetails($projectId);
            if (!$project) {
                return $this->respondWithNotFound('项目不存在');
            }

            // 获取主流程节点实例
            $mainNodes = $this->nodeInstanceModel->getProjectNodes($projectId, 0);

            // 获取协同流程
            $collaborations = $this->collaborationModel->getProjectCollaborations($projectId);
            $collabNodes = [];
            
            foreach ($collaborations as $collab) {
                $collabNodes[$collab['id']] = [
                    'collaboration' => $collab,
                    'nodes' => $this->nodeInstanceModel->getProjectNodes($projectId, $collab['id'])
                ];
            }

            $result = [
                'project' => $project,
                'main_flow' => $mainNodes,
                'collaboration_flows' => $collabNodes
            ];

            return $this->respond($result, '获取项目流程状态成功');

        } catch (\Exception $e) {
            return $this->respondWithServerError('获取项目流程状态失败', $e);
        }
    }

    /**
     * 流转到下一个节点
     * POST /api/workflow/{projectId}/flow
     */
    public function flowToNext($projectId)
    {
        try {
            $data = $this->getAllInput();

            // 验证必需参数
            $required = ['current_node_template_id', 'trigger_condition', 'user_uid'];
            $errors = $this->validateRequired($required, $data);
            if ($errors) {
                return $this->respondWithValidationError($errors);
            }

            $collaborationId = (int)$this->getInput('collaboration_id', 0);
            $actionData = $this->getInput('action_data', []);

            $nextNodeTemplateId = $this->workflowEngine->flowToNextNode(
                $projectId,
                $data['current_node_template_id'],
                $data['trigger_condition'],
                $data['user_uid'],
                $collaborationId,
                $actionData
            );

            if ($nextNodeTemplateId === null) {
                $message = $collaborationId == 0 ? '主流程已完成' : '协同流程已完成';
                return $this->respond(['completed' => true], $message);
            }

            $this->logAction('flow_to_next', [
                'project_id' => $projectId,
                'collaboration_id' => $collaborationId,
                'from_node' => $data['current_node_template_id'],
                'to_node' => $nextNodeTemplateId,
                'trigger_condition' => $data['trigger_condition']
            ], $data['user_uid']);

            return $this->respond([
                'next_node_template_id' => $nextNodeTemplateId,
                'completed' => false
            ], '流程流转成功');

        } catch (\Exception $e) {
            return $this->respondWithServerError('流程流转失败', $e);
        }
    }

    /**
     * 创建协同部门流程
     * POST /api/workflow/{projectId}/collaborations
     */
    public function createCollaborations($projectId)
    {
        try {
            $data = $this->getAllInput();

            // 验证必需参数
            $required = ['department_ids', 'user_uid'];
            $errors = $this->validateRequired($required, $data);
            if ($errors) {
                return $this->respondWithValidationError($errors);
            }

            $departmentIds = $data['department_ids'];
            if (!is_array($departmentIds) || empty($departmentIds)) {
                return $this->respondWithError('协同部门ID列表不能为空', 400);
            }

            // 这里需要实现创建协同流程的逻辑
            // 暂时返回成功响应
            $collaborationIds = [];
            foreach ($departmentIds as $deptId) {
                $collaborationData = [
                    'project_id' => $projectId,
                    'department_id' => $deptId,
                    'status' => 0,
                    'creator' => $data['user_uid']
                ];
                $collaborationId = $this->collaborationModel->insert($collaborationData);
                $collaborationIds[] = $collaborationId;
            }

            $this->logAction('create_collaborations', [
                'project_id' => $projectId,
                'department_ids' => $departmentIds,
                'collaboration_ids' => $collaborationIds
            ], $data['user_uid']);

            return $this->respond([
                'collaboration_ids' => $collaborationIds
            ], '创建协同流程成功', 201);

        } catch (\Exception $e) {
            return $this->respondWithServerError('创建协同流程失败', $e);
        }
    }

    /**
     * 更新节点处理人员
     * PUT /api/workflow/{projectId}/nodes/{nodeInstanceId}/assignee
     */
    public function updateNodeAssignee($projectId, $nodeInstanceId)
    {
        try {
            $data = $this->getAllInput();

            // 验证必需参数
            $required = ['assignee_uid', 'user_uid'];
            $errors = $this->validateRequired($required, $data);
            if ($errors) {
                return $this->respondWithValidationError($errors);
            }

            $nodeInstance = $this->nodeInstanceModel->find($nodeInstanceId);
            if (!$nodeInstance || $nodeInstance['project_id'] != $projectId) {
                return $this->respondWithNotFound('节点实例不存在');
            }

            $result = $this->nodeInstanceModel->updateAssignee(
                $nodeInstanceId,
                $data['assignee_uid'],
                $data['user_uid']
            );

            if (!$result) {
                return $this->respondWithError('更新处理人员失败', 500);
            }

            $this->logAction('update_node_assignee', [
                'project_id' => $projectId,
                'node_instance_id' => $nodeInstanceId,
                'assignee_uid' => $data['assignee_uid']
            ], $data['user_uid']);

            return $this->respond(null, '更新处理人员成功');

        } catch (\Exception $e) {
            return $this->respondWithServerError('更新处理人员失败', $e);
        }
    }

    /**
     * 获取节点操作历史
     * GET /api/workflow/{projectId}/nodes/{nodeTemplateId}/history
     */
    public function getNodeHistory($projectId, $nodeTemplateId)
    {
        try {
            $collaborationId = (int)$this->getInput('collaboration_id', 0);

            $history = $this->nodeInstanceModel->getNodeHistory($projectId, $nodeTemplateId, $collaborationId);

            return $this->respond($history, '获取节点历史成功');

        } catch (\Exception $e) {
            return $this->respondWithServerError('获取节点历史失败', $e);
        }
    }

    /**
     * 获取项目操作日志
     * GET /api/workflow/{projectId}/logs
     */
    public function getProjectLogs($projectId)
    {
        try {
            $page = (int)$this->getInput('page', 1);
            $limit = (int)$this->getInput('limit', 50);
            $collaborationId = $this->getInput('collaboration_id');

            $offset = ($page - 1) * $limit;
            $logs = $this->nodeInstanceModel->getProjectActionLogs($projectId, $collaborationId, $limit, $offset);

            // 简单实现，实际应该有总数统计
            return $this->respond($logs, '获取操作日志成功');

        } catch (\Exception $e) {
            return $this->respondWithServerError('获取操作日志失败', $e);
        }
    }
}
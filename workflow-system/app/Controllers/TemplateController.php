<?php

namespace App\Controllers;

use App\Models\NodeTemplateModel;
use App\Models\WorkflowDefinitionModel;
use App\Models\NodeFlowRuleModel;
use App\Models\ProductLineModel;
use App\Models\RoleModel;

/**
 * 模板管理控制器
 * 处理工作流模板、节点模板、流转规则的管理
 */
class TemplateController extends BaseController
{
    protected $nodeTemplateModel;
    protected $workflowDefinitionModel;
    protected $flowRuleModel;
    protected $productLineModel;
    protected $roleModel;

    public function __construct()
    {
        $this->nodeTemplateModel = new NodeTemplateModel();
        $this->workflowDefinitionModel = new WorkflowDefinitionModel();
        $this->flowRuleModel = new NodeFlowRuleModel();
        $this->productLineModel = new ProductLineModel();
        $this->roleModel = new RoleModel();
    }

    /**
     * 显示模板管理页面
     * GET /templates
     */
    public function index(): string
    {
        // 重置响应头为HTML格式，因为BaseController默认设置为JSON
        $this->response->setHeader('Content-Type', 'text/html; charset=utf-8');
        
        $data = [
            'title' => '流程模板管理',
            'current_page' => 'templates',
            'breadcrumb' => '流程模板 / 模板管理'
        ];
        
        return view('templates/index', $data);
    }

    /**
     * 获取节点模板列表
     * GET /api/templates/nodes
     */
    public function getNodeTemplates()
    {
        try {
            $nodeType = $this->getInput('node_type');
            $page = (int)$this->getInput('page', 1);
            $limit = (int)$this->getInput('limit', 50);

            if ($nodeType !== null) {
                $templates = $this->nodeTemplateModel->getByNodeType($nodeType);
                return $this->respond($templates, '获取节点模板成功');
            } else {
                $templates = $this->nodeTemplateModel->getAllWithRoles();
                return $this->respond($templates, '获取节点模板成功');
            }

        } catch (\Exception $e) {
            return $this->respondWithServerError('获取节点模板失败', $e);
        }
    }

    /**
     * 获取产品线工作流定义
     * GET /api/templates/workflows/{productLineId}
     */
    public function getWorkflowDefinition($productLineId)
    {
        try {
            $productLine = $this->productLineModel->find($productLineId);
            if (!$productLine) {
                return $this->respondWithNotFound('产品线不存在');
            }

            // 获取主流程
            $mainFlow = $this->workflowDefinitionModel->getMainFlow($productLineId);

            // 获取协同流程
            $collabFlow = $this->workflowDefinitionModel->getCollaborationFlow($productLineId);

            $result = [
                'product_line' => $productLine,
                'main_flow' => $mainFlow,
                'collaboration_flow' => $collabFlow
            ];

            return $this->respond($result, '获取工作流定义成功');

        } catch (\Exception $e) {
            return $this->respondWithServerError('获取工作流定义失败', $e);
        }
    }

    /**
     * 创建节点模板
     * POST /api/templates/nodes
     */
    public function createNodeTemplate()
    {
        try {
            $data = $this->getAllInput();

            // 验证必需参数
            $required = ['node_code', 'node_name', 'assignee_role_id', 'node_type'];
            $errors = $this->validateRequired($required, $data);
            if ($errors) {
                return $this->respondWithValidationError($errors);
            }

            // 验证节点编码唯一性
            $existing = $this->nodeTemplateModel->getByNodeCode($data['node_code']);
            if ($existing) {
                return $this->respondWithError('节点编码已存在', 400);
            }

            // 验证角色是否存在
            $role = $this->roleModel->find($data['assignee_role_id']);
            if (!$role) {
                return $this->respondWithError('指定的角色不存在', 400);
            }

            $data['creator'] = $this->getInput('user_uid', 'system');
            $result = $this->nodeTemplateModel->insert($data);

            if (!$result) {
                return $this->respondWithError('创建节点模板失败', 500);
            }

            $this->logAction('create_node_template', [
                'template_id' => $this->nodeTemplateModel->getInsertID(),
                'node_code' => $data['node_code']
            ], $data['creator']);

            return $this->respond(['template_id' => $this->nodeTemplateModel->getInsertID()], '创建节点模板成功', 201);

        } catch (\Exception $e) {
            return $this->respondWithServerError('创建节点模板失败', $e);
        }
    }

    /**
     * 更新节点模板
     * PUT /api/templates/nodes/{id}
     */
    public function updateNodeTemplate($id)
    {
        try {
            $template = $this->nodeTemplateModel->find($id);
            if (!$template) {
                return $this->respondWithNotFound('节点模板不存在');
            }

            $data = $this->getAllInput();
            $updaterUid = $this->getInput('user_uid', 'system');

            // 只允许更新特定字段
            $allowedFields = ['node_name', 'assignee_role_id', 'is_optional', 'action_config'];
            $updateData = [];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }

            if (empty($updateData)) {
                return $this->respondWithError('没有需要更新的数据', 400);
            }

            $updateData['updater'] = $updaterUid;
            $result = $this->nodeTemplateModel->update($id, $updateData);

            if (!$result) {
                return $this->respondWithError('更新节点模板失败', 500);
            }

            $this->logAction('update_node_template', [
                'template_id' => $id,
                'data' => $updateData
            ], $updaterUid);

            return $this->respond(null, '更新节点模板成功');

        } catch (\Exception $e) {
            return $this->respondWithServerError('更新节点模板失败', $e);
        }
    }

    /**
     * 删除节点模板
     * DELETE /api/templates/nodes/{id}
     */
    public function delete($id)
    {
        try {
            $template = $this->nodeTemplateModel->find($id);
            if (!$template) {
                return $this->respondWithNotFound('节点模板不存在');
            }

            $deleterUid = $this->getInput('user_uid', 'system');

            // 检查模板是否正在使用中
            $inUse = $this->workflowDefinitionModel->where(['node_template_id' => $id, 'deleted_at' => 0])->first();
            if ($inUse) {
                return $this->respondWithError('节点模板正在使用中，无法删除', 400);
            }

            $result = $this->nodeTemplateModel->update($id, [
                'deleted_at' => time(),
                'deleter' => $deleterUid
            ]);

            if (!$result) {
                return $this->respondWithError('删除节点模板失败', 500);
            }

            $this->logAction('delete_node_template', ['template_id' => $id], $deleterUid);

            return $this->respond(null, '删除节点模板成功');

        } catch (\Exception $e) {
            return $this->respondWithServerError('删除节点模板失败', $e);
        }
    }

    /**
     * 获取所有流转规则
     * GET /api/v1/templates/flow-rules
     */
    public function getAllFlowRules()
    {
        try {
            $rules = $this->flowRuleModel->getAllWithNodeNames();
            return $this->respond($rules, '获取流转规则成功');
        } catch (\Exception $e) {
            return $this->respondWithServerError('获取流转规则失败', $e);
        }
    }

    /**
     * 创建流转规则
     * POST /api/v1/templates/flow-rules
     */
    public function createFlowRule()
    {
        try {
            $data = $this->getAllInput();

            // 验证必需参数
            $required = ['source_node_id', 'target_node_id', 'condition_type', 'priority'];
            $errors = $this->validateRequired($required, $data);
            if ($errors) {
                return $this->respondWithValidationError($errors);
            }

            // 转换为实际表字段名
            $dbData = [
                'from_node_template_id' => $data['source_node_id'],
                'to_node_template_id' => $data['target_node_id'],
                'condition_type' => $data['condition_type'],
                'condition_value' => $data['condition_value'] ?? '',
                'condition_expression' => '',
                'priority' => $data['priority'],
                'is_active' => $data['is_active'] ?? true,
                'product_line_id' => 0, // 默认为0，表示通用规则
                'creator' => $this->getInput('user_uid', 'system'),
                'created_at' => time()
            ];

            // 验证节点是否存在
            $sourceNode = $this->nodeTemplateModel->find($data['source_node_id']);
            if (!$sourceNode) {
                return $this->respondWithError('来源节点不存在', 400);
            }

            $targetNode = $this->nodeTemplateModel->find($data['target_node_id']);
            if (!$targetNode) {
                return $this->respondWithError('目标节点不存在', 400);
            }

            // 检查规则是否已存在
            $existing = $this->flowRuleModel->where([
                'from_node_template_id' => $data['source_node_id'],
                'to_node_template_id' => $data['target_node_id'],
                'condition_type' => $data['condition_type'],
                'deleted_at' => 0
            ])->first();
            
            if ($existing) {
                return $this->respondWithError('相同的流转规则已存在', 400);
            }

            $result = $this->flowRuleModel->insert($dbData);

            if (!$result) {
                return $this->respondWithError('创建流转规则失败', 500);
            }

            $this->logAction('create_flow_rule', [
                'rule_id' => $this->flowRuleModel->getInsertID(),
                'from_node_template_id' => $data['source_node_id'],
                'to_node_template_id' => $data['target_node_id']
            ], $dbData['creator']);

            return $this->respond(['rule_id' => $this->flowRuleModel->getInsertID()], '创建流转规则成功', 201);

        } catch (\Exception $e) {
            return $this->respondWithServerError('创建流转规则失败', $e);
        }
    }

    /**
     * 更新流转规则
     * PUT /api/v1/templates/flow-rules/{id}
     */
    public function updateFlowRule($id)
    {
        try {
            $rule = $this->flowRuleModel->find($id);
            if (!$rule) {
                return $this->respondWithNotFound('流转规则不存在');
            }

            $data = $this->getAllInput();
            $updaterUid = $this->getInput('user_uid', 'system');

            // 允许更新的字段
            $allowedFields = ['target_node_id', 'condition_type', 'condition_value', 'priority', 'is_active'];
            $updateData = [];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    // 转换字段名
                    if ($field === 'target_node_id') {
                        $updateData['to_node_template_id'] = $data[$field];
                    } else {
                        $updateData[$field] = $data[$field];
                    }
                }
            }

            if (empty($updateData)) {
                return $this->respondWithError('没有需要更新的数据', 400);
            }

            $updateData['updater'] = $updaterUid;
            $updateData['updated_at'] = time();
            $result = $this->flowRuleModel->update($id, $updateData);

            if (!$result) {
                return $this->respondWithError('更新流转规则失败', 500);
            }

            $this->logAction('update_flow_rule', [
                'rule_id' => $id,
                'data' => $updateData
            ], $updaterUid);

            return $this->respond(null, '更新流转规则成功');

        } catch (\Exception $e) {
            return $this->respondWithServerError('更新流转规则失败', $e);
        }
    }

    /**
     * 删除流转规则
     * DELETE /api/v1/templates/flow-rules/{id}
     */
    public function deleteFlowRule($id)
    {
        try {
            $rule = $this->flowRuleModel->find($id);
            if (!$rule) {
                return $this->respondWithNotFound('流转规则不存在');
            }

            $deleterUid = $this->getInput('user_uid', 'system');

            $result = $this->flowRuleModel->delete($id);

            if (!$result) {
                return $this->respondWithError('删除流转规则失败', 500);
            }

            $this->logAction('delete_flow_rule', ['rule_id' => $id], $deleterUid);

            return $this->respond(null, '删除流转规则成功');

        } catch (\Exception $e) {
            return $this->respondWithServerError('删除流转规则失败', $e);
        }
    }

    /**
     * 导出模板配置
     * GET /api/templates/export/{productLineId}
     */
    public function exportTemplate($productLineId)
    {
        try {
            $productLine = $this->productLineModel->find($productLineId);
            if (!$productLine) {
                return $this->respondWithNotFound('产品线不存在');
            }

            // 获取工作流定义
            $mainFlow = $this->workflowDefinitionModel->getMainFlow($productLineId);
            $collabFlow = $this->workflowDefinitionModel->getCollaborationFlow($productLineId);

            // 获取流转规则
            $flowRules = $this->flowRuleModel->getProductLineFlowRules($productLineId);

            $exportData = [
                'product_line' => $productLine,
                'main_flow' => $mainFlow,
                'collaboration_flow' => $collabFlow,
                'flow_rules' => $flowRules,
                'export_time' => date('Y-m-d H:i:s'),
                'version' => '2.0'
            ];

            return $this->respond($exportData, '导出模板配置成功');

        } catch (\Exception $e) {
            return $this->respondWithServerError('导出模板配置失败', $e);
        }
    }
}
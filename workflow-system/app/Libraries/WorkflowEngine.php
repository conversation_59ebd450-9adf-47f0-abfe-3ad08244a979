<?php

namespace App\Libraries;

use App\Models\ProjectModel;
use App\Models\ProjectCollaborationModel;
use App\Models\ProjectNodeInstanceModel;
use App\Models\NodeFlowRuleModel;
use App\Models\WorkflowDefinitionModel;
use App\Models\NodeTemplateModel;
use App\Models\WorkflowContextModel;
use App\Models\WorkflowStateLogModel;
use App\Models\ProjectActionLogModel;

/**
 * 工作流引擎核心类
 * 负责项目流程的流转控制、节点实例管理、协同创建等核心功能
 */
class WorkflowEngine
{
    protected $projectModel;
    protected $collaborationModel;
    protected $nodeInstanceModel;
    protected $flowRuleModel;
    protected $workflowDefinitionModel;
    protected $nodeTemplateModel;
    protected $contextModel;
    protected $stateLogModel;
    protected $actionLogModel;

    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->collaborationModel = new ProjectCollaborationModel();
        $this->nodeInstanceModel = new ProjectNodeInstanceModel();
        $this->flowRuleModel = new NodeFlowRuleModel();
        $this->workflowDefinitionModel = new WorkflowDefinitionModel();
        $this->nodeTemplateModel = new NodeTemplateModel();
        $this->contextModel = new WorkflowContextModel();
        $this->stateLogModel = new WorkflowStateLogModel();
        $this->actionLogModel = new ProjectActionLogModel();
    }

    /**
     * 创建项目并初始化工作流
     */
    public function createProject($projectData, $creatorUid)
    {
        $db = \Config\Database::connect();
        $db->transStart();

        try {
            // 1. 创建项目
            $projectData['creator'] = $creatorUid;
            $projectData['status'] = 1; // 进行中
            $projectId = $this->projectModel->insert($projectData);

            if (!$projectId) {
                throw new \Exception('创建项目失败');
            }

            // 2. 获取产品线的起始节点
            $startNode = $this->workflowDefinitionModel->getStartNodeByProductLine(
                $projectData['product_line_id'], 
                1 // 主流程
            );

            if (!$startNode) {
                throw new \Exception('未找到产品线的起始节点');
            }

            // 3. 创建起始节点实例
            $this->createNodeInstance($projectId, 0, $startNode['node_template_id'], $creatorUid);

            // 4. 更新项目当前节点
            $this->projectModel->updateCurrentNode($projectId, $startNode['node_template_id'], $creatorUid);

            // 5. 启动起始节点
            $this->startNode($projectId, $startNode['node_template_id'], 0, $creatorUid);

            $db->transComplete();

            if ($db->transStatus() === false) {
                throw new \Exception('事务执行失败');
            }

            return $projectId;

        } catch (\Exception $e) {
            $db->transRollback();
            throw $e;
        }
    }

    /**
     * 流转到下一个节点
     */
    public function flowToNextNode($projectId, $currentNodeTemplateId, $triggerCondition, $triggerUserUid, $collaborationId = 0, $actionData = [])
    {
        $db = \Config\Database::connect();
        $db->transStart();

        try {
            // 1. 获取项目信息
            $project = $this->projectModel->find($projectId);
            if (!$project) {
                throw new \Exception('项目不存在');
            }

            // 2. 根据流转规则查找下一个节点
            $nextNodeTemplateId = $this->findNextNode($project['product_line_id'], $currentNodeTemplateId, $triggerCondition, $projectId, $collaborationId);

            if (!$nextNodeTemplateId) {
                // 没有找到下一个节点，可能是流程结束
                $this->completeFlow($projectId, $collaborationId, $triggerUserUid);
                $db->transComplete();
                return null;
            }

            // 3. 完成当前节点
            $this->completeCurrentNode($projectId, $currentNodeTemplateId, $collaborationId, $actionData, $triggerUserUid);

            // 4. 创建或激活下一个节点实例
            $this->createOrActivateNextNode($projectId, $nextNodeTemplateId, $collaborationId, $triggerUserUid);

            // 5. 更新项目/协同当前节点
            if ($collaborationId == 0) {
                $this->projectModel->updateCurrentNode($projectId, $nextNodeTemplateId, $triggerUserUid);
            } else {
                $this->collaborationModel->updateCurrentNode($collaborationId, $nextNodeTemplateId, $triggerUserUid);
            }

            // 6. 记录状态变更日志
            $this->logStateChange($projectId, $collaborationId, $currentNodeTemplateId, $nextNodeTemplateId, $triggerCondition, $triggerUserUid);

            // 7. 记录操作日志
            $this->logAction($projectId, $collaborationId, $currentNodeTemplateId, 2, $triggerUserUid, $actionData, '流转到下一节点');

            $db->transComplete();

            if ($db->transStatus() === false) {
                throw new \Exception('事务执行失败');
            }

            return $nextNodeTemplateId;

        } catch (\Exception $e) {
            $db->transRollback();
            throw $e;
        }
    }

    /**
     * 创建节点实例
     */
    protected function createNodeInstance($projectId, $collaborationId, $nodeTemplateId, $creatorUid)
    {
        $data = [
            'project_id' => $projectId,
            'collaboration_id' => $collaborationId,
            'node_template_id' => $nodeTemplateId,
            'status' => 0, // 未开始
            'creator' => $creatorUid
        ];
        
        return $this->nodeInstanceModel->insert($data);
    }

    /**
     * 启动节点
     */
    protected function startNode($projectId, $nodeTemplateId, $collaborationId, $assigneeUid)
    {
        $instance = $this->nodeInstanceModel->getNodeInstance($projectId, $nodeTemplateId, $collaborationId);
        if ($instance) {
            return $this->nodeInstanceModel->update($instance['id'], [
                'status' => 1, // 进行中
                'assignee_uid' => $assigneeUid,
                'started_at' => time()
            ]);
        }
        return false;
    }

    /**
     * 完成当前节点
     */
    protected function completeCurrentNode($projectId, $nodeTemplateId, $collaborationId, $resultData, $completerUid)
    {
        $instance = $this->nodeInstanceModel->getNodeInstance($projectId, $nodeTemplateId, $collaborationId);
        if ($instance) {
            return $this->nodeInstanceModel->update($instance['id'], [
                'status' => 2, // 已完成
                'finished_at' => time(),
                'result_data' => json_encode($resultData),
                'updater' => $completerUid
            ]);
        }
        return false;
    }

    /**
     * 创建或激活下一个节点
     */
    protected function createOrActivateNextNode($projectId, $nextNodeTemplateId, $collaborationId, $creatorUid)
    {
        // 检查节点实例是否已存在
        $instance = $this->nodeInstanceModel->getNodeInstance($projectId, $nextNodeTemplateId, $collaborationId);
        
        if (!$instance) {
            // 创建新实例
            $this->createNodeInstance($projectId, $collaborationId, $nextNodeTemplateId, $creatorUid);
        }
        
        // 激活节点
        return $this->startNode($projectId, $nextNodeTemplateId, $collaborationId, $creatorUid);
    }

    /**
     * 完成流程
     */
    protected function completeFlow($projectId, $collaborationId, $completerUid)
    {
        if ($collaborationId == 0) {
            // 主流程完成
            $this->projectModel->updateProjectStatus($projectId, 2, $completerUid); // 已结束
        } else {
            // 协同流程完成
            $this->collaborationModel->update($collaborationId, [
                'status' => 2, // 已完成
                'updater' => $completerUid
            ]);
        }
    }

    /**
     * 根据条件查找下一个节点
     */
    protected function findNextNode($productLineId, $fromNodeTemplateId, $triggerCondition, $projectId, $collaborationId)
    {
        // 这里需要根据流转规则来确定下一个节点
        // 暂时返回null，后续实现完整的规则引擎
        return null;
    }

    /**
     * 记录状态变更日志
     */
    protected function logStateChange($projectId, $collaborationId, $fromNodeTemplateId, $toNodeTemplateId, $triggerCondition, $triggerUserUid)
    {
        $data = [
            'project_id' => $projectId,
            'collaboration_id' => $collaborationId,
            'from_node_template_id' => $fromNodeTemplateId,
            'to_node_template_id' => $toNodeTemplateId,
            'trigger_condition' => $triggerCondition,
            'trigger_user_uid' => $triggerUserUid,
            'creator' => $triggerUserUid
        ];
        
        return $this->stateLogModel->insert($data);
    }

    /**
     * 记录操作日志
     */
    protected function logAction($projectId, $collaborationId, $nodeTemplateId, $actionType, $actionByUid, $actionData, $remark)
    {
        $data = [
            'project_id' => $projectId,
            'collaboration_id' => $collaborationId,
            'node_template_id' => $nodeTemplateId,
            'action_type' => $actionType,
            'action_by_uid' => $actionByUid,
            'action_data' => json_encode($actionData),
            'remark' => $remark,
            'creator' => $actionByUid
        ];
        
        return $this->actionLogModel->insert($data);
    }
}
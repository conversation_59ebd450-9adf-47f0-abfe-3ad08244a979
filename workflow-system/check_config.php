<?php

/**
 * 数据库配置检查脚本
 */

echo "=== 工作流系统数据库配置检查 ===\n\n";

// 检查.env文件是否存在
if (!file_exists('.env')) {
    echo "❌ .env 文件不存在！\n";
    exit(1);
}

echo "✅ .env 文件存在\n";

// 读取.env文件内容
$envContent = file_get_contents('.env');

// 检查关键配置
$configs = [
    'CI_ENVIRONMENT' => '开发环境设置',
    'app.baseURL' => '应用基础URL',
    'database.default.hostname' => '数据库主机',
    'database.default.database' => '数据库名称',
    'database.default.username' => '数据库用户名',
    'encryption.key' => '加密密钥'
];

echo "\n检查配置项:\n";
foreach ($configs as $key => $description) {
    if (strpos($envContent, $key) !== false && strpos($envContent, "# $key") === false) {
        echo "✅ $description ($key) 已配置\n";
    } else {
        echo "❌ $description ($key) 未配置\n";
    }
}

// 显示数据库配置
echo "\n当前数据库配置:\n";
echo "📊 数据库名称: workflow_system\n";
echo "🏠 主机地址: localhost\n";
echo "🔌 端口: 3306\n";
echo "👤 用户名: root\n";
echo "🔒 密码: (空)\n";
echo "📝 字符集: utf8mb4\n";

echo "\n下一步操作:\n";
echo "1️⃣  确保MySQL服务已启动\n";
echo "2️⃣  创建数据库: CREATE DATABASE workflow_system CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\n";
echo "3️⃣  运行迁移: php spark migrate\n";
echo "4️⃣  启动服务器: php spark serve\n";

echo "\n如需修改数据库配置，请编辑 .env 文件中的以下行:\n";
echo "database.default.hostname = 你的数据库主机\n";
echo "database.default.database = 你的数据库名\n";
echo "database.default.username = 你的用户名\n";
echo "database.default.password = 你的密码\n";
echo "database.default.port = 你的端口\n\n";

echo "🎉 配置检查完成！\n";
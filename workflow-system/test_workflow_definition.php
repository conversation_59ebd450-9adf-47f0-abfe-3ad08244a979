<?php

// 测试工作流定义创建API
require_once 'vendor/autoload.php';

// 设置环境
$_ENV['CI_ENVIRONMENT'] = 'development';

// 初始化CodeIgniter
$app = \Config\Services::codeigniter();
$app->initialize();

// 创建请求对象
$request = \Config\Services::request();

// 模拟POST数据
$postData = [
    'product_line_id' => 1,
    'node_template_id' => 1,
    'flow_type' => 1,
    'order_no' => 1
];

echo "=== 测试工作流定义创建API ===\n";
echo "发送数据: " . json_encode($postData, JSON_UNESCAPED_UNICODE) . "\n\n";

try {
    // 创建控制器实例
    $controller = new \App\Controllers\TemplateController();
    
    // 模拟请求数据
    $request->setBody(json_encode($postData));
    
    // 调用创建方法
    $response = $controller->createWorkflowDefinition();
    
    // 获取响应内容
    if (method_exists($response, 'getBody')) {
        $responseBody = $response->getBody();
    } else {
        $responseBody = $response;
    }
    
    echo "API响应: " . $responseBody . "\n";
    
    // 解析响应
    $responseData = json_decode($responseBody, true);
    
    if ($responseData && isset($responseData['status'])) {
        if ($responseData['status'] === 'success') {
            echo "✅ 工作流定义创建成功!\n";
            echo "定义ID: " . ($responseData['data']['definition_id'] ?? 'N/A') . "\n";
        } else {
            echo "❌ 工作流定义创建失败!\n";
            echo "错误信息: " . ($responseData['message'] ?? 'Unknown error') . "\n";
            if (isset($responseData['errors'])) {
                echo "详细错误: " . json_encode($responseData['errors'], JSON_UNESCAPED_UNICODE) . "\n";
            }
        }
    } else {
        echo "❌ 无效的响应格式\n";
    }
    
} catch (\Exception $e) {
    echo "❌ 异常错误: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "错误堆栈:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== 测试完成 ===\n";
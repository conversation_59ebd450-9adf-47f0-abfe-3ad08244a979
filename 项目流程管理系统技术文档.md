# 项目流程管理系统 - 完整技术文档

## 📋 目录
1. [业务需求分析](#业务需求分析)
2. [系统架构设计](#系统架构设计)
3. [数据库设计详解](#数据库设计详解)
4. [工作流引擎设计](#工作流引擎设计)
5. [前端Demo说明](#前端Demo说明)
6. [关键技术决策](#关键技术决策)
7. [后续开发指南](#后续开发指南)

---

## 业务需求分析

### 核心业务场景
销售签单后的项目流程管理，涵盖4条产品线（A/B/C/D），每条产品线有不同的流程规则。

### 主要角色
- **销售**：项目发起人，更新产品信息，决定签约状态
- **主部门负责人**：选择执行人员，审批方案，选择协同部门
- **主部门执行人员**：上传方案资料，确认数据，上传PPT
- **协同部门负责人**：选择项目执行人员，审核数据
- **协同部门执行人员**：上传原始数据
- **数据分析师**：上传原始数据（A产品线）
- **商务负责人**：审核未中标项目

### 流程特点

#### 1. 主流程（所有产品线共有）
```
main_1: 项目发起 (销售)
main_2: 选择主部门执行人员 (主部门负责人)
main_3: 上传方案或资料 (主部门执行人员)
main_4: 审批方案或资料 (主部门负责人)
main_5: 更新产品信息 (销售) - 关键分支点
```

#### 2. 签约状态分支
- **未中标**：main_5 → 商务审核 → 项目结束
- **中标**：main_5 → main_6（各产品线不同）

#### 3. 各产品线差异

**产品线A（复杂协同）**：
- main_6: 选择协同部门 → 协同流程（4个节点）→ main_7: 确认数据&上传PPT → main_8: 审批PPT&跟进客户

**产品线B（无协同）**：
- main_6: 选择执行人员 → main_7: 一键完成&交付 → 项目结束

**产品线C（简单协同）**：
- main_6: 选择协同部门 → 协同流程（2个节点）→ main_7: 确认数据&上传PPT → main_8: 审批PPT&交付

**产品线D（可跳过审批）**：
- main_6: 选择协同部门 → 协同流程（3个节点）→ main_7: 数据质检&上传PPT → main_8: 审批PPT(可跳过)&跟进客户

#### 4. 协同流程特点
- **动态创建**：只有在main_6选择协同部门后才实例化
- **多部门并行**：一个项目可选择多个协同部门，每个部门独立执行
- **标准化流程**：每个协同部门执行相同的协同子流程模板

#### 5. 驳回机制
- **重置状态**：驳回时重置目标节点及后续所有节点状态
- **追溯驳回**：支持驳回已确认的数据
- **版本管理**：每次重置后版本号递增

---

## 系统架构设计

### 整体架构
```
前端展示层 (HTML/JS)
    ↓
业务逻辑层 (工作流引擎)
    ↓
数据持久层 (MySQL)
```

### 核心模块

#### 1. 工作流模板管理
- 节点模板复用
- 产品线流程定义
- 流转规则配置

#### 2. 项目执行引擎
- 节点实例化
- 状态流转
- 条件判断

#### 3. 协同管理
- 动态协同创建
- 多部门并行执行
- 数据隔离

---

## 数据库设计详解

### V1设计问题
原始设计存在以下问题：
1. **节点模板不可复用**：每个产品线都要重复定义相同节点
2. **流转逻辑硬编码**：没有配置化的流转规则
3. **缺乏条件分支支持**：无法处理复杂的业务判断

### V2设计方案（推荐）

#### 核心表结构

**1. 工作流模板系统**
```sql
-- 通用节点模板（可复用）
node_template:
- node_code: main_1, main_2, collab_1...
- node_name: 项目发起, 选择执行人员...
- assignee_role_id: 处理角色
- node_type: 1主流程/2协同流程/3分支决策/4汇聚
- is_optional: 是否可跳过

-- 产品线工作流定义（组装模板）
workflow_definition:
- product_line_id + node_template_id + flow_type
- order_no: 流程中的顺序
- is_start_node/is_end_node: 起始/结束标记

-- 节点流转规则（条件流转）
node_flow_rule:
- from_node_template_id → to_node_template_id
- condition_type: default/sign_status/approve_result/reject
- condition_value: 具体条件值
- condition_expression: 复杂条件表达式
```

**2. 项目执行系统**
```sql
-- 项目主表
project:
- current_node_template_id: 当前节点模板ID（替代hard-coded节点编码）

-- 协同部门管理
project_collaboration:
- 每个选择的协同部门一条记录
- current_node_template_id: 当前协同节点

-- 节点实例表
project_node_instance:
- node_template_id: 关联模板
- collaboration_id: 区分主流程(0)和协同流程
- status: 节点状态
- result_data: JSON格式的处理结果
```

**3. 工作流引擎辅助**
```sql
-- 执行上下文（存储流程变量）
workflow_context:
- context_key/context_value: 键值对
- 如：sign_status=中标, selected_departments=[3,4,5]

-- 状态变更日志
workflow_state_log:
- 记录每次节点流转的完整信息
- 支持流程回放和审计
```

### 数据流转示例

#### 节点模板定义
```sql
-- 定义通用节点模板
INSERT INTO node_template VALUES 
(1, 'main_1', '项目发起', 1, 1, 0),
(2, 'main_5', '更新产品信息', 1, 3, 0), -- 分支决策节点
(3, 'main_6_choose_collab', '选择协同部门', 2, 1, 0),
(4, 'main_6_choose_executor', '选择执行人员', 2, 1, 0);
```

#### 产品线流程组装
```sql
-- 产品线A使用协同流程
INSERT INTO workflow_definition VALUES
(1, 1, 1, 1, 1, 0), -- A线使用main_1
(1, 2, 1, 5, 0, 0), -- A线使用main_5
(1, 3, 1, 6, 0, 0); -- A线使用main_6_choose_collab

-- 产品线B不使用协同
INSERT INTO workflow_definition VALUES
(2, 1, 1, 1, 1, 0), -- B线使用main_1
(2, 2, 1, 5, 0, 0), -- B线使用main_5
(2, 4, 1, 6, 0, 0); -- B线使用main_6_choose_executor
```

#### 流转规则配置
```sql
-- 正常流转
INSERT INTO node_flow_rule VALUES
(1, 2, 3, 'sign_status', '中标'), -- A线：main_5→main_6_choose_collab（中标时）
(2, 2, 4, 'sign_status', '中标'); -- B线：main_5→main_6_choose_executor（中标时）

-- 驳回流转
INSERT INTO node_flow_rule VALUES
(1, 4, 3, 'reject', ''); -- main_4驳回→main_3
```

---

## 工作流引擎设计

### 核心算法

#### 1. 节点流转算法
```python
def flow_to_next_node(project_id, current_node_id, trigger_condition):
    # 1. 查询当前项目和节点状态
    project = get_project(project_id)
    current_node = get_node_instance(project_id, current_node_id)
    
    # 2. 根据流转规则查找下一个节点
    rules = get_flow_rules(
        product_line_id=project.product_line_id,
        from_node=current_node.node_template_id,
        condition=trigger_condition
    )
    
    # 3. 按优先级匹配条件
    for rule in rules.order_by('priority'):
        if evaluate_condition(rule, project):
            next_node_template = rule.to_node_template_id
            break
    
    # 4. 创建下一个节点实例
    create_node_instance(project_id, next_node_template)
    
    # 5. 更新项目状态
    update_project_current_node(project_id, next_node_template)
    
    # 6. 记录状态变更日志
    log_state_change(project_id, current_node_id, next_node_template, trigger_condition)
```

#### 2. 协同流程实例化
```python
def create_collaboration_instances(project_id, selected_departments):
    product_line = get_project_product_line(project_id)
    
    # 获取该产品线的协同流程模板
    collab_templates = get_workflow_definition(
        product_line_id=product_line.id,
        flow_type=2  # 协同流程
    ).order_by('order_no')
    
    for dept_id in selected_departments:
        # 1. 创建协同记录
        collab = create_project_collaboration(project_id, dept_id)
        
        # 2. 为该部门创建所有协同节点实例
        for template in collab_templates:
            create_node_instance(
                project_id=project_id,
                collaboration_id=collab.id,
                node_template_id=template.node_template_id,
                status=0  # 未开始
            )
        
        # 3. 激活第一个协同节点
        first_template = collab_templates[0]
        activate_node_instance(project_id, collab.id, first_template.node_template_id)
```

#### 3. 条件表达式引擎
```python
def evaluate_condition(rule, project):
    if rule.condition_type == 'default':
        return True
    elif rule.condition_type == 'sign_status':
        return project.sign_status == rule.condition_value
    elif rule.condition_type == 'approve_result':
        return get_workflow_context(project.id, 'last_approve_result') == rule.condition_value
    elif rule.condition_expression:
        # 支持复杂表达式：sign_status=中标 AND collab_count>0
        return eval_expression(rule.condition_expression, project)
    else:
        return False
```

### 关键特性

#### 1. 模板复用机制
- **一次定义，多处使用**：main_1节点在所有产品线都可以复用
- **个性化组装**：不同产品线选择不同的节点组合
- **版本管理**：模板修改后，新项目自动使用新版本

#### 2. 动态协同创建
- **按需实例化**：只有选择协同部门时才创建协同流程
- **多部门并行**：每个协同部门有独立的节点实例链
- **状态隔离**：各协同部门的进度互不影响

#### 3. 灵活流转控制
- **条件分支**：支持根据业务状态进行流转判断
- **优先级机制**：多个匹配规则按优先级执行
- **表达式支持**：复杂条件可用表达式描述

---

## 前端Demo说明

### 当前实现文件
- `project-management-demo.html`：完整的项目管理系统Demo
- `template-config.html`：简单的模板配置页面
- `backend-demo/mock-data.json`：模拟数据

### Demo功能

#### 1. 项目管理标签页
- **项目列表**：卡片式展示，支持搜索和筛选
- **添加项目**：完整的表单，包括产品线选择和主部门分配
- **编辑项目**：修改项目基本信息
- **删除项目**：安全删除确认

#### 2. 流程模板标签页
- **模板查看**：左右分栏显示主流程和协同流程模板
- **节点CRUD**：支持添加、编辑、删除模板节点
- **产品线切换**：选择不同产品线查看对应模板
- **导入导出**：JSON格式的模板配置导入导出

#### 3. 流程跟踪标签页
- **进度展示**：时间线方式显示项目当前状态
- **协同部门选择**：在main_6节点提供选择协同部门功能
- **动态协同显示**：选择协同部门后显示各部门的独立流程

### Mock数据结构
```javascript
mockData = {
    productLines: [A, B, C, D],
    roles: [销售, 主部门负责人, ...],
    departments: [技术部, 运营部, ...],
    projects: [项目实例],
    nodeTemplates: {
        A: { main: [...], collab: [...] },
        B: { main: [...] },  // B无协同流程
        C: { main: [...], collab: [...] },
        D: { main: [...], collab: [...] }
    }
}
```

### 关键设计决策

#### 1. 协同流程的展示逻辑
- **模板阶段**：显示协同子流程模板，说明"运行时动态创建"
- **执行阶段**：只有选择协同部门后才显示具体的协同实例
- **多部门展示**：每个协同部门显示独立的进度条

#### 2. 节点编辑功能
- **双类型支持**：主流程节点和协同流程节点分别管理
- **自动编码**：新增节点时自动生成node_code
- **顺序管理**：删除节点后自动重新排序

---

## 关键技术决策

### 1. 模板 vs 实例
**决策**：采用模板-实例分离的设计
- **模板**：存储在`node_template`和`workflow_definition`，定义流程形态
- **实例**：存储在`project_node_instance`，记录具体执行状态
- **优势**：模板可复用，实例记录完整，支持历史回溯

### 2. 协同流程的处理方式
**决策**：动态实例化 + 多部门并行
- **不在模板中预设具体部门**：协同模板是通用的
- **运行时按需创建**：主节点6选择后才实例化
- **独立状态管理**：每个协同部门有独立的`collaboration_id`

### 3. 流转规则的配置化
**决策**：基于规则表的条件流转
- **避免硬编码**：流转逻辑存储在`node_flow_rule`表
- **支持复杂条件**：condition_expression字段支持表达式
- **优先级机制**：多个匹配规则按priority排序

### 4. 驳回机制的设计
**决策**：状态重置 + 版本管理
- **重置后续节点**：驳回时将目标节点及后续节点状态重置
- **版本递增**：每次重置后version字段递增
- **支持追溯驳回**：可以驳回已确认的数据

---

## 后续开发指南

### 阶段1：数据库实现
1. **执行schema_v2.sql**创建新的表结构
2. **编写数据迁移脚本**从V1迁移到V2
3. **插入基础数据**：角色、部门、产品线、节点模板

### 阶段2：工作流引擎开发
1. **节点流转引擎**：实现condition evaluation和next node calculation
2. **协同管理模块**：实现动态协同创建和状态管理
3. **API接口层**：提供RESTful API供前端调用

### 阶段3：前端重构
1. **数据层重构**：将mock数据替换为API调用
2. **模板管理优化**：支持V2的复用模板设计
3. **流程可视化**：增加流程图展示和编辑功能

### 阶段4：高级功能
1. **表达式引擎**：支持复杂的条件表达式
2. **流程设计器**：可视化的流程设计工具
3. **报表统计**：项目进度统计和分析

### 关键API设计

#### 工作流相关
```
GET /api/templates/{product_line_id}  # 获取产品线模板
POST /api/templates/nodes             # 创建节点模板
PUT /api/templates/nodes/{id}         # 更新节点模板
DELETE /api/templates/nodes/{id}      # 删除节点模板

GET /api/workflow/rules/{product_line_id}  # 获取流转规则
POST /api/workflow/rules                   # 创建流转规则
```

#### 项目执行相关
```
POST /api/projects                    # 创建项目
GET /api/projects/{id}/nodes          # 获取项目节点状态
POST /api/projects/{id}/flow          # 执行节点流转
POST /api/projects/{id}/collaborations # 选择协同部门

POST /api/projects/{id}/actions       # 记录节点操作
GET /api/projects/{id}/logs           # 获取操作日志
```

### 数据库索引优化
```sql
-- 高频查询索引
CREATE INDEX idx_project_current_node ON project(current_node_template_id);
CREATE INDEX idx_flow_rule_lookup ON node_flow_rule(product_line_id, from_node_template_id);
CREATE INDEX idx_node_instance_active ON project_node_instance(project_id, status);
CREATE INDEX idx_workflow_context_lookup ON workflow_context(project_id, collaboration_id, context_key);
```

### 性能考虑
1. **缓存策略**：模板数据和流转规则适合缓存
2. **分页查询**：项目列表和操作日志需要分页
3. **异步处理**：邮件发送和文件处理可异步执行
4. **数据归档**：历史项目数据定期归档

---

## 总结

这个项目流程管理系统的核心是**工作流引擎**，通过模板复用、条件流转、动态协同等机制，实现了灵活可配置的业务流程管理。

**关键设计原则**：
1. **模板与实例分离**：提高复用性和可维护性
2. **配置化流转**：避免硬编码，支持业务变化
3. **动态协同创建**：按需实例化，节省资源
4. **完整状态跟踪**：支持审计和问题排查

**技术栈建议**：
- 后端：PHP/Java + MySQL
- 前端：Vue.js/React + Element UI
- 缓存：Redis
- 队列：RabbitMQ/Redis Queue

这份文档记录了所有关键的设计决策和实现细节，后续开发时可以避免重复思考和设计偏差。
